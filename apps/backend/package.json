{"name": "@nurse-platform/backend", "version": "0.0.1", "private": true, "main": "dist/main.js", "nx": {"targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "@nurse-platform/backend:build", "runBuildTargetDependencies": false, "watch": true, "inspect": true}, "configurations": {"development": {"buildTarget": "@nurse-platform/backend:build:development", "watch": true}, "production": {"buildTarget": "@nurse-platform/backend:build:production", "watch": false}}}}}, "dependencies": {"@nestjs/config": "^4.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "joi": "^17.13.3", "openai": "^5.10.1"}}