import { Injectable, NotFoundException, ForbiddenException, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Application, ApplicationDocument, ApplicationStatus } from '../schemas/application.schema';
import { PatientRequest, PatientRequestDocument, RequestStatus } from '../schemas/patient-request.schema';
import { User, UserDocument, UserRole } from '../schemas/user.schema';
import { NurseProfile, NurseProfileDocument } from '../schemas/nurse-profile.schema';
import { CreateApplicationDto, UpdateApplicationStatusDto, WithdrawApplicationDto } from '../dto/application.dto';

@Injectable()
export class ApplicationsService {
  constructor(
    @InjectModel(Application.name) private applicationModel: Model<ApplicationDocument>,
    @InjectModel(PatientRequest.name) private requestModel: Model<PatientRequestDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(NurseProfile.name) private nurseProfileModel: Model<NurseProfileDocument>,
  ) {}

  private compareObjectIds(id1: any, id2: any): boolean {
    return id1 && id2 && id1.toString() === id2.toString();
  }

  async applyToRequest(requestId: string, createApplicationDto: CreateApplicationDto, nurseUser: UserDocument) {
    // Validate nurse role
    if (nurseUser.role !== UserRole.NURSE) {
      throw new ForbiddenException('Only nurses can apply to requests');
    }

    // Check if nurse is verified (temporarily disabled for testing)
    // if (nurseUser.status !== 'verified') {
    //   throw new ForbiddenException('Only verified nurses can apply to requests');
    // }

    // Find the request
    const request = await this.requestModel.findById(requestId).exec();
    if (!request) {
      throw new NotFoundException('Request not found');
    }

    // Check if request is still pending
    if (request.status !== RequestStatus.PENDING) {
      throw new BadRequestException('Can only apply to pending requests');
    }

    // Check if nurse already applied to this request
    const existingApplication = await this.applicationModel.findOne({
      nurseId: nurseUser._id,
      requestId: requestId,
    }).exec();

    if (existingApplication) {
      throw new ConflictException('You have already applied to this request');
    }

    // Get nurse profile for additional validation (temporarily disabled for testing)
    // const nurseProfile = await this.nurseProfileModel.findOne({ userId: nurseUser._id }).exec();
    // if (!nurseProfile || !nurseProfile.isAvailable) {
    //   throw new ForbiddenException('Nurse profile not found or not available');
    // }

    // Create application
    const application = new this.applicationModel({
      nurseId: nurseUser._id,
      requestId: requestId,
      patientId: request.patientId,
      status: ApplicationStatus.PENDING,
      appliedAt: new Date(),
      ...createApplicationDto,
      estimatedStartTime: createApplicationDto.estimatedStartTime ? new Date(createApplicationDto.estimatedStartTime) : undefined,
    });

    await application.save();

    // Populate the application with related data
    const populatedApplication = await this.applicationModel
      .findById(application._id)
      .populate('nurseId', '-password')
      .populate('requestId')
      .populate('patientId', '-password')
      .exec();

    return {
      message: 'Application submitted successfully',
      application: populatedApplication,
    };
  }

  async getMyApplications(nurseUser: UserDocument) {
    if (nurseUser.role !== UserRole.NURSE) {
      throw new ForbiddenException('Only nurses can view their applications');
    }

    const applications = await this.applicationModel
      .find({ nurseId: nurseUser._id })
      .populate('requestId')
      .populate('patientId', '-password')
      .sort({ createdAt: -1 })
      .exec();

    return {
      message: 'Applications retrieved successfully',
      applications,
      total: applications.length,
    };
  }

  async getApplicationsForRequest(requestId: string, user: UserDocument) {
    // Find the request
    const request = await this.requestModel.findById(requestId).exec();
    if (!request) {
      throw new NotFoundException('Request not found');
    }

    // Check permissions - only patient who owns the request or admin can view applications
    if (user.role !== UserRole.ADMIN && !this.compareObjectIds(request.patientId, user._id)) {
      throw new ForbiddenException('You can only view applications for your own requests');
    }

    const applications = await this.applicationModel
      .find({ requestId: requestId })
      .populate('nurseId', '-password')
      .populate({
        path: 'nurseId',
        populate: {
          path: 'nurseProfile',
          model: 'NurseProfile',
          select: 'rating totalReviews completedJobs specializations hourlyRate',
        },
      })
      .sort({ createdAt: -1 })
      .exec();

    return {
      message: 'Applications retrieved successfully',
      applications,
      total: applications.length,
    };
  }

  async updateApplicationStatus(applicationId: string, updateStatusDto: UpdateApplicationStatusDto, user: UserDocument) {
    const application = await this.applicationModel
      .findById(applicationId)
      .populate('requestId')
      .exec();

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    const request = application.requestId as any;

    // Check permissions - only patient who owns the request or admin can update status
    if (user.role !== UserRole.ADMIN && !this.compareObjectIds(request.patientId, user._id)) {
      throw new ForbiddenException('You can only update applications for your own requests');
    }

    // Validate status transitions
    if (updateStatusDto.status === ApplicationStatus.ACCEPTED) {
      if (application.status !== ApplicationStatus.PENDING) {
        throw new BadRequestException('Only pending applications can be accepted');
      }

      // Check if request is still pending
      if (request.status !== RequestStatus.PENDING) {
        throw new BadRequestException('Request is no longer available');
      }

      // Reject all other applications for this request
      await this.applicationModel.updateMany(
        { 
          requestId: request._id,
          _id: { $ne: application._id },
          status: ApplicationStatus.PENDING 
        },
        { 
          status: ApplicationStatus.REJECTED,
          respondedAt: new Date(),
          respondedBy: user._id,
          responseNotes: 'Another nurse was selected for this request'
        }
      );

      // Update request status to in_progress and assign nurse
      await this.requestModel.findByIdAndUpdate(request._id, {
        status: RequestStatus.IN_PROGRESS,
        nurseId: application.nurseId,
        acceptedAt: new Date(),
      });
    }

    // Update application
    application.status = updateStatusDto.status;
    application.respondedAt = new Date();
    application.respondedBy = user._id as any;
    application.responseNotes = updateStatusDto.responseNotes;

    await application.save();

    const updatedApplication = await this.applicationModel
      .findById(application._id)
      .populate('nurseId', '-password')
      .populate('requestId')
      .populate('patientId', '-password')
      .exec();

    return {
      message: 'Application status updated successfully',
      application: updatedApplication,
    };
  }

  async withdrawApplication(applicationId: string, withdrawDto: WithdrawApplicationDto, nurseUser: UserDocument) {
    if (nurseUser.role !== UserRole.NURSE) {
      throw new ForbiddenException('Only nurses can withdraw their applications');
    }

    const application = await this.applicationModel.findById(applicationId).exec();
    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // Check if nurse owns this application
    if (!this.compareObjectIds(application.nurseId, nurseUser._id)) {
      throw new ForbiddenException('You can only withdraw your own applications');
    }

    // Check if application can be withdrawn
    if (application.status !== ApplicationStatus.PENDING) {
      throw new BadRequestException('Only pending applications can be withdrawn');
    }

    // Update application
    application.status = ApplicationStatus.WITHDRAWN;
    application.withdrawnAt = new Date();
    application.withdrawalReason = withdrawDto.withdrawalReason;

    await application.save();

    return {
      message: 'Application withdrawn successfully',
      application,
    };
  }

  async getAllApplications(user: UserDocument) {
    if (user.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can view all applications');
    }

    const applications = await this.applicationModel
      .find()
      .populate('nurseId', '-password')
      .populate('requestId')
      .populate('patientId', '-password')
      .sort({ createdAt: -1 })
      .exec();

    return {
      message: 'All applications retrieved successfully',
      applications,
      total: applications.length,
    };
  }
}
