import { Controller, Post, Get, Put, Body, Param, Query, UseGuards, Request, ValidationPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { ApplicationsService } from './applications.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { CreateApplicationDto, UpdateApplicationStatusDto, WithdrawApplicationDto, ApplicationResponseDto } from '../dto/application.dto';

@ApiTags('Applications')
@Controller('api/applications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ApplicationsController {
  constructor(private readonly applicationsService: ApplicationsService) {}

  @Post('apply/:requestId')
  @ApiOperation({ summary: 'Apply to a patient request' })
  @ApiParam({ name: 'requestId', description: 'Patient request ID' })
  @ApiResponse({ status: 201, description: 'Application submitted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - invalid data or request not available' })
  @ApiResponse({ status: 403, description: 'Forbidden - only verified nurses can apply' })
  @ApiResponse({ status: 404, description: 'Request not found' })
  @ApiResponse({ status: 409, description: 'Conflict - already applied to this request' })
  async applyToRequest(
    @Param('requestId') requestId: string,
    @Body(ValidationPipe) createApplicationDto: CreateApplicationDto,
    @Request() req: any
  ) {
    return this.applicationsService.applyToRequest(requestId, createApplicationDto, req.user);
  }

  @Get('my-applications')
  @ApiOperation({ summary: 'Get nurse\'s own applications' })
  @ApiResponse({ status: 200, description: 'Applications retrieved successfully', type: [ApplicationResponseDto] })
  @ApiResponse({ status: 403, description: 'Forbidden - only nurses can view their applications' })
  async getMyApplications(@Request() req: any) {
    return this.applicationsService.getMyApplications(req.user);
  }

  @Get('request/:requestId')
  @ApiOperation({ summary: 'Get applications for a specific request' })
  @ApiParam({ name: 'requestId', description: 'Patient request ID' })
  @ApiResponse({ status: 200, description: 'Applications retrieved successfully', type: [ApplicationResponseDto] })
  @ApiResponse({ status: 403, description: 'Forbidden - can only view applications for own requests' })
  @ApiResponse({ status: 404, description: 'Request not found' })
  async getApplicationsForRequest(
    @Param('requestId') requestId: string,
    @Request() req: any
  ) {
    return this.applicationsService.getApplicationsForRequest(requestId, req.user);
  }

  @Put(':applicationId/status')
  @ApiOperation({ summary: 'Update application status (accept/reject)' })
  @ApiParam({ name: 'applicationId', description: 'Application ID' })
  @ApiResponse({ status: 200, description: 'Application status updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - invalid status transition' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only update applications for own requests' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  async updateApplicationStatus(
    @Param('applicationId') applicationId: string,
    @Body(ValidationPipe) updateStatusDto: UpdateApplicationStatusDto,
    @Request() req: any
  ) {
    return this.applicationsService.updateApplicationStatus(applicationId, updateStatusDto, req.user);
  }

  @Put(':applicationId/withdraw')
  @ApiOperation({ summary: 'Withdraw application' })
  @ApiParam({ name: 'applicationId', description: 'Application ID' })
  @ApiResponse({ status: 200, description: 'Application withdrawn successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - cannot withdraw non-pending application' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only withdraw own applications' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  async withdrawApplication(
    @Param('applicationId') applicationId: string,
    @Body(ValidationPipe) withdrawDto: WithdrawApplicationDto,
    @Request() req: any
  ) {
    return this.applicationsService.withdrawApplication(applicationId, withdrawDto, req.user);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all applications (admin only)' })
  @ApiResponse({ status: 200, description: 'All applications retrieved successfully', type: [ApplicationResponseDto] })
  @ApiResponse({ status: 403, description: 'Forbidden - admin only' })
  async getAllApplications(@Request() req: any) {
    return this.applicationsService.getAllApplications(req.user);
  }
}
