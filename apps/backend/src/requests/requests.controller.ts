import { Controller, Post, Get, Patch, Body, Param, Query, UseGuards, Request, ValidationPipe } from '@nestjs/common';
import { RequestsService } from './requests.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { CreateRequestDto, UpdateRequestStatusDto } from '../dto/request.dto';
import { RequestStatus } from '../schemas/patient-request.schema';

@Controller('api/requests')
export class RequestsController {
  constructor(private readonly requestsService: RequestsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  async createRequest(@Body(ValidationPipe) createRequestDto: CreateRequestDto, @Request() req : any) {
    console.log('🔍 Controller createRequest called');
    console.log('🔍 req.user:', req.user);
    console.log('🔍 createRequestDto:', createRequestDto);
    return this.requestsService.createRequest(createRequestDto, req.user);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async getRequests(@Request() req : any, @Query('status') status?: RequestStatus) {
    return this.requestsService.getRequests(req.user, status);
  }

  @Get('available')
  async getAvailableRequests() {
    return this.requestsService.getAvailableRequests();
  }

  @Get('dashboard/stats')
  @UseGuards(JwtAuthGuard)
  async getDashboardStats(@Request() req : any) {
    return this.requestsService.getDashboardStats(req.user);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async getRequestById(@Param('id') requestId: string, @Request() req : any) {
    return this.requestsService.getRequestById(requestId, req.user);
  }

  @Patch(':id/status')
  @UseGuards(JwtAuthGuard)
  async updateRequestStatus(
    @Param('id') requestId: string,
    @Body(ValidationPipe) updateStatusDto: UpdateRequestStatusDto,
    @Request() req : any
  ) {
    return this.requestsService.updateRequestStatus(requestId, updateStatusDto, req.user);
  }
}
