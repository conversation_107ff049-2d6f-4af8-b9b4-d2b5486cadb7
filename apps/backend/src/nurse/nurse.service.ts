import { Injectable, NotFoundException, ForbiddenException, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Application, ApplicationDocument, ApplicationStatus } from '../schemas/application.schema';
import { PatientRequest, PatientRequestDocument, RequestStatus } from '../schemas/patient-request.schema';
import { User, UserDocument } from '../schemas/user.schema';
import { NurseProfile, NurseProfileDocument } from '../schemas/nurse-profile.schema';
import { CreateApplicationDto, WithdrawApplicationDto } from '../dto/application.dto';
import { UpdateRequestStatusDto } from '../dto/request.dto';

@Injectable()
export class NurseService {
  constructor(
    @InjectModel(Application.name) private applicationModel: Model<ApplicationDocument>,
    @InjectModel(PatientRequest.name) private requestModel: Model<PatientRequestDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(NurseProfile.name) private nurseProfileModel: Model<NurseProfileDocument>,
  ) {}

  private compareObjectIds(id1: any, id2: any): boolean {
    return id1 && id2 && id1.toString() === id2.toString();
  }

  async applyToRequest(requestId: string, createApplicationDto: CreateApplicationDto, nurseUser: UserDocument) {
    // Check if nurse is verified (temporarily disabled for testing)
    // if (nurseUser.status !== 'verified') {
    //   throw new ForbiddenException('Only verified nurses can apply to requests');
    // }

    // Find the request
    const request = await this.requestModel.findById(requestId).populate('patientId', '-password').exec();
    if (!request) {
      throw new NotFoundException('Request not found');
    }

    // Check if request is still pending
    if (request.status !== RequestStatus.PENDING) {
      throw new BadRequestException('Can only apply to pending requests');
    }

    // Check if nurse already applied to this request
    const existingApplication = await this.applicationModel.findOne({
      nurseId: nurseUser._id,
      requestId: requestId,
    }).exec();

    if (existingApplication) {
      throw new ConflictException('You have already applied to this request');
    }

    // Get nurse profile for additional validation (temporarily disabled for testing)
    // const nurseProfile = await this.nurseProfileModel.findOne({ userId: nurseUser._id }).exec();
    // if (!nurseProfile || !nurseProfile.isAvailable) {
    //   throw new ForbiddenException('Nurse profile not found or not available');
    // }

    // Create application
    const application = new this.applicationModel({
      nurseId: nurseUser._id,
      requestId: requestId,
      patientId: request.patientId,
      status: ApplicationStatus.PENDING,
      appliedAt: new Date(),
      ...createApplicationDto,
      estimatedStartTime: createApplicationDto.estimatedStartTime ? new Date(createApplicationDto.estimatedStartTime) : undefined,
    });

    await application.save();

    // Log the application for notification purposes
    console.log(`📧 New nurse application: ${nurseUser.name} applied to request "${request.title}" for patient ${request.patientId}`);

    return {
      message: 'Application submitted successfully',
      application: {
        id: application._id,
        status: application.status,
        appliedAt: application.appliedAt,
        request: {
          id: request._id,
          title: request.title,
          description: request.description,
          serviceType: request.serviceType,
          scheduledDate: request.scheduledDate,
          budget: request.budget,
        },
      },
    };
  }

  async getMyApplications(nurseUser: UserDocument) {
    const applications = await this.applicationModel
      .find({ nurseId: nurseUser._id })
      .populate({
        path: 'requestId',
        populate: {
          path: 'patientId',
          select: '-password',
        },
      })
      .sort({ createdAt: -1 })
      .exec();

    const formattedApplications = applications.map(app => ({
      id: app._id,
      status: app.status,
      coverLetter: app.coverLetter,
      proposedRate: app.proposedRate,
      estimatedStartTime: app.estimatedStartTime,
      availabilityNotes: app.availabilityNotes,
      appliedAt: app.appliedAt,
      respondedAt: app.respondedAt,
      responseNotes: app.responseNotes,
      withdrawnAt: app.withdrawnAt,
      withdrawalReason: app.withdrawalReason,
      request: app.requestId ? {
        id: (app.requestId as any)._id,
        title: (app.requestId as any).title,
        description: (app.requestId as any).description,
        serviceType: (app.requestId as any).serviceType,
        status: (app.requestId as any).status,
        scheduledDate: (app.requestId as any).scheduledDate,
        budget: (app.requestId as any).budget,
        address: (app.requestId as any).address,
        patient: (app.requestId as any).patientId ? {
          id: (app.requestId as any).patientId._id,
          name: (app.requestId as any).patientId.name,
          email: (app.requestId as any).patientId.email,
        } : null,
      } : null,
      createdAt: app.createdAt,
      updatedAt: app.updatedAt,
    }));

    return {
      message: 'Applications retrieved successfully',
      applications: formattedApplications,
      total: applications.length,
    };
  }

  async getAvailableRequests(nurseUser: UserDocument) {
    // Get nurse's applied request IDs to exclude them
    const appliedRequests = await this.applicationModel
      .find({ nurseId: nurseUser._id })
      .select('requestId')
      .exec();
    
    const appliedRequestIds = appliedRequests.map(app => app.requestId);

    // Find pending requests that nurse hasn't applied to
    const requests = await this.requestModel
      .find({
        status: RequestStatus.PENDING,
        _id: { $nin: appliedRequestIds },
      })
      .populate('patientId', '-password')
      .sort({ createdAt: -1 })
      .exec();

    const formattedRequests = requests.map(request => ({
      id: request._id,
      title: request.title,
      description: request.description,
      serviceType: request.serviceType,
      status: request.status,
      location: request.location,
      address: request.address,
      scheduledDate: request.scheduledDate,
      estimatedDuration: request.estimatedDuration,
      urgencyLevel: request.urgencyLevel,
      specialRequirements: request.specialRequirements,
      budget: request.budget,
      contactPhone: request.contactPhone,
      notes: request.notes,
      patient: request.patientId ? {
        id: (request.patientId as any)._id,
        name: (request.patientId as any).name,
        email: (request.patientId as any).email,
        address: (request.patientId as any).address,
      } : null,
      createdAt: request.createdAt,
    }));

    return {
      message: 'Available requests retrieved successfully',
      requests: formattedRequests,
      total: requests.length,
    };
  }

  async getMyRequests(nurseUser: UserDocument) {
    const requests = await this.requestModel
      .find({ nurseId: nurseUser._id })
      .populate('patientId', '-password')
      .sort({ createdAt: -1 })
      .exec();

    const formattedRequests = requests.map(request => ({
      id: request._id,
      title: request.title,
      description: request.description,
      serviceType: request.serviceType,
      status: request.status,
      location: request.location,
      address: request.address,
      scheduledDate: request.scheduledDate,
      estimatedDuration: request.estimatedDuration,
      urgencyLevel: request.urgencyLevel,
      specialRequirements: request.specialRequirements,
      budget: request.budget,
      contactPhone: request.contactPhone,
      notes: request.notes,
      acceptedAt: request.acceptedAt,
      completedAt: request.completedAt,
      cancelledAt: request.cancelledAt,
      cancellationReason: request.cancellationReason,
      patient: request.patientId ? {
        id: (request.patientId as any)._id,
        name: (request.patientId as any).name,
        email: (request.patientId as any).email,
        phone: (request.patientId as any).phone,
        address: (request.patientId as any).address,
      } : null,
      createdAt: request.createdAt,
      updatedAt: request.updatedAt,
    }));

    return {
      message: 'Assigned requests retrieved successfully',
      requests: formattedRequests,
      total: requests.length,
    };
  }

  async updateRequestStatus(requestId: string, updateStatusDto: UpdateRequestStatusDto, nurseUser: UserDocument) {
    const request = await this.requestModel.findById(requestId).exec();
    if (!request) {
      throw new NotFoundException('Request not found');
    }

    // Check if nurse is assigned to this request
    if (!this.compareObjectIds(request.nurseId, nurseUser._id)) {
      throw new ForbiddenException('You can only update requests assigned to you');
    }

    const { status, cancellationReason } = updateStatusDto;

    // Validate status transitions
    if (status === RequestStatus.WORK_IN_PROGRESS) {
      if (request.status !== RequestStatus.IN_PROGRESS) {
        throw new BadRequestException('Can only start work on accepted requests');
      }
    } else if (status === RequestStatus.COMPLETED) {
      if (request.status !== RequestStatus.WORK_IN_PROGRESS && request.status !== RequestStatus.IN_PROGRESS) {
        throw new BadRequestException('Can only complete requests that are in progress');
      }
      request.completedAt = new Date();
    } else if (status === RequestStatus.CANCELLED) {
      if (request.status === RequestStatus.COMPLETED) {
        throw new BadRequestException('Cannot cancel completed requests');
      }
      if (!cancellationReason) {
        throw new BadRequestException('Cancellation reason is required');
      }
      request.cancelledAt = new Date();
      request.cancellationReason = cancellationReason;
    }

    request.status = status;
    await request.save();

    return {
      message: 'Request status updated successfully',
      request: {
        id: request._id,
        status: request.status,
        completedAt: request.completedAt,
        cancelledAt: request.cancelledAt,
        cancellationReason: request.cancellationReason,
      },
    };
  }

  async withdrawApplication(applicationId: string, withdrawDto: WithdrawApplicationDto, nurseUser: UserDocument) {
    const application = await this.applicationModel.findById(applicationId).exec();
    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // Check if nurse owns this application
    if (!this.compareObjectIds(application.nurseId, nurseUser._id)) {
      throw new ForbiddenException('You can only withdraw your own applications');
    }

    // Check if application can be withdrawn
    if (application.status !== ApplicationStatus.PENDING) {
      throw new BadRequestException('Only pending applications can be withdrawn');
    }

    // Update application
    application.status = ApplicationStatus.WITHDRAWN;
    application.withdrawnAt = new Date();
    application.withdrawalReason = withdrawDto.withdrawalReason;

    await application.save();

    return {
      message: 'Application withdrawn successfully',
      application: {
        id: application._id,
        status: application.status,
        withdrawnAt: application.withdrawnAt,
        withdrawalReason: application.withdrawalReason,
      },
    };
  }

  async getDashboardStats(nurseUser: UserDocument) {
    const [
      totalApplications,
      pendingApplications,
      acceptedApplications,
      totalRequests,
      inProgressRequests,
      completedRequests,
    ] = await Promise.all([
      this.applicationModel.countDocuments({ nurseId: nurseUser._id }),
      this.applicationModel.countDocuments({ nurseId: nurseUser._id, status: ApplicationStatus.PENDING }),
      this.applicationModel.countDocuments({ nurseId: nurseUser._id, status: ApplicationStatus.ACCEPTED }),
      this.requestModel.countDocuments({ nurseId: nurseUser._id }),
      this.requestModel.countDocuments({ nurseId: nurseUser._id, status: { $in: [RequestStatus.IN_PROGRESS, RequestStatus.WORK_IN_PROGRESS] } }),
      this.requestModel.countDocuments({ nurseId: nurseUser._id, status: RequestStatus.COMPLETED }),
    ]);

    // Calculate earnings (assuming average rate)
    const nurseProfile = await this.nurseProfileModel.findOne({ userId: nurseUser._id }).exec();
    const hourlyRate = nurseProfile?.hourlyRate || 150;
    const estimatedEarnings = completedRequests * hourlyRate * 4; // Assuming 4 hours average per request

    return {
      message: 'Dashboard statistics retrieved successfully',
      stats: {
        applications: {
          total: totalApplications,
          pending: pendingApplications,
          accepted: acceptedApplications,
          rejected: totalApplications - pendingApplications - acceptedApplications,
        },
        requests: {
          total: totalRequests,
          inProgress: inProgressRequests,
          completed: completedRequests,
          cancelled: totalRequests - inProgressRequests - completedRequests,
        },
        earnings: {
          estimated: estimatedEarnings,
          hourlyRate: hourlyRate,
          completedJobs: completedRequests,
        },
        profile: {
          rating: nurseProfile?.rating || 0,
          totalReviews: nurseProfile?.totalReviews || 0,
          isAvailable: nurseProfile?.isAvailable || false,
        },
      },
    };
  }
}
