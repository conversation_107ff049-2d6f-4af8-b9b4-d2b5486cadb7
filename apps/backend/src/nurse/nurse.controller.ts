import { Controller, Post, Get, Put, Body, Param, Query, UseGuards, Request, ValidationPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { NurseService } from './nurse.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard, Roles } from '../auth/roles.guard';
import { UserRole } from '../schemas/user.schema';
import { CreateApplicationDto, WithdrawApplicationDto } from '../dto/application.dto';
import { UpdateRequestStatusDto } from '../dto/request.dto';

@ApiTags('Nurse')
@Controller('api/nurse')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.NURSE)
@ApiBearerAuth()
export class NurseController {
  constructor(private readonly nurseService: NurseService) {}

  @Post('apply-to-request/:requestId')
  @ApiOperation({ summary: 'Apply to a patient request' })
  @ApiParam({ name: 'requestId', description: 'Patient request ID' })
  @ApiResponse({ status: 201, description: 'Application submitted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden - only verified nurses can apply' })
  @ApiResponse({ status: 404, description: 'Request not found' })
  @ApiResponse({ status: 409, description: 'Already applied to this request' })
  async applyToRequest(
    @Param('requestId') requestId: string,
    @Body(ValidationPipe) createApplicationDto: CreateApplicationDto,
    @Request() req: any
  ) {
    return this.nurseService.applyToRequest(requestId, createApplicationDto, req.user);
  }

  @Get('my-applications')
  @ApiOperation({ summary: 'Get nurse\'s applications' })
  @ApiResponse({ status: 200, description: 'Applications retrieved successfully' })
  async getMyApplications(@Request() req: any) {
    return this.nurseService.getMyApplications(req.user);
  }

  @Get('available-requests')
  @ApiOperation({ summary: 'Get available requests for nurses' })
  @ApiResponse({ status: 200, description: 'Available requests retrieved successfully' })
  async getAvailableRequests(@Request() req: any) {
    return this.nurseService.getAvailableRequests(req.user);
  }

  @Get('my-requests')
  @ApiOperation({ summary: 'Get nurse\'s assigned requests' })
  @ApiResponse({ status: 200, description: 'Assigned requests retrieved successfully' })
  async getMyRequests(@Request() req: any) {
    return this.nurseService.getMyRequests(req.user);
  }

  @Put('update-request-status/:requestId')
  @ApiOperation({ summary: 'Update request status (work_in_progress, completed, cancelled)' })
  @ApiParam({ name: 'requestId', description: 'Request ID' })
  @ApiResponse({ status: 200, description: 'Request status updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - invalid status transition' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only update assigned requests' })
  @ApiResponse({ status: 404, description: 'Request not found' })
  async updateRequestStatus(
    @Param('requestId') requestId: string,
    @Body(ValidationPipe) updateStatusDto: UpdateRequestStatusDto,
    @Request() req: any
  ) {
    return this.nurseService.updateRequestStatus(requestId, updateStatusDto, req.user);
  }

  @Put('withdraw-application/:applicationId')
  @ApiOperation({ summary: 'Withdraw application' })
  @ApiParam({ name: 'applicationId', description: 'Application ID' })
  @ApiResponse({ status: 200, description: 'Application withdrawn successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - cannot withdraw non-pending application' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only withdraw own applications' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  async withdrawApplication(
    @Param('applicationId') applicationId: string,
    @Body(ValidationPipe) withdrawDto: WithdrawApplicationDto,
    @Request() req: any
  ) {
    return this.nurseService.withdrawApplication(applicationId, withdrawDto, req.user);
  }

  @Get('dashboard-stats')
  @ApiOperation({ summary: 'Get nurse dashboard statistics' })
  @ApiResponse({ status: 200, description: 'Dashboard statistics retrieved successfully' })
  async getDashboardStats(@Request() req: any) {
    return this.nurseService.getDashboardStats(req.user);
  }
}
