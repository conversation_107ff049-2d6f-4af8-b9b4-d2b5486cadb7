import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ApplicationDocument = Application & Document;

export enum ApplicationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
}

@Schema({ timestamps: true })
export class Application {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  nurseId!: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'PatientRequest', required: true })
  requestId!: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  patientId!: Types.ObjectId;

  @Prop({ default: ApplicationStatus.PENDING, enum: ApplicationStatus })
  status!: ApplicationStatus;

  @Prop()
  coverLetter?: string;

  @Prop()
  proposedRate?: number;

  @Prop()
  estimatedStartTime?: Date;

  @Prop()
  availabilityNotes?: string;

  @Prop()
  appliedAt?: Date;

  @Prop()
  respondedAt?: Date;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  respondedBy?: Types.ObjectId;

  @Prop()
  responseNotes?: string;

  @Prop()
  withdrawnAt?: Date;

  @Prop()
  withdrawalReason?: string;

  // Timestamps (automatically added by Mongoose)
  createdAt?: Date;
  updatedAt?: Date;
}

export const ApplicationSchema = SchemaFactory.createForClass(Application);

// Create indexes for better query performance
ApplicationSchema.index({ nurseId: 1, createdAt: -1 });
ApplicationSchema.index({ requestId: 1, createdAt: -1 });
ApplicationSchema.index({ patientId: 1, createdAt: -1 });
ApplicationSchema.index({ status: 1 });
ApplicationSchema.index({ nurseId: 1, requestId: 1 }, { unique: true }); // One application per nurse per request
