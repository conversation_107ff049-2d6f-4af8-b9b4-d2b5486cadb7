import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsDateString, IsEnum, MaxLength, Min } from 'class-validator';
import { ApplicationStatus } from '../schemas/application.schema';

export class CreateApplicationDto {
  @ApiPropertyOptional({
    description: 'Cover letter or message to the patient',
    example: 'I am experienced in post-surgical care and would be happy to help with your recovery.',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: 'Cover letter must not exceed 1000 characters' })
  coverLetter?: string;

  @ApiPropertyOptional({
    description: 'Proposed hourly rate in EGP',
    example: 150,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Proposed rate must be a number' })
  proposedRate?: number;

  @ApiPropertyOptional({
    description: 'Estimated start time for the service',
    example: '2024-01-15T10:00:00Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Estimated start time must be a valid date' })
  estimatedStartTime?: string;

  @ApiPropertyOptional({
    description: 'Notes about availability or scheduling preferences',
    example: 'Available weekdays 9 AM - 5 PM',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Availability notes must not exceed 500 characters' })
  availabilityNotes?: string;
}

export class UpdateApplicationStatusDto {
  @ApiProperty({
    description: 'New application status',
    enum: ApplicationStatus,
    example: ApplicationStatus.ACCEPTED,
  })
  @IsEnum(ApplicationStatus, { message: 'Invalid application status' })
  status!: ApplicationStatus;

  @ApiPropertyOptional({
    description: 'Response notes from patient or admin',
    example: 'Application accepted. Please contact patient to arrange schedule.',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Response notes must not exceed 500 characters' })
  responseNotes?: string;
}

export class WithdrawApplicationDto {
  @ApiProperty({
    description: 'Reason for withdrawing the application',
    example: 'Schedule conflict - no longer available',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255, { message: 'Withdrawal reason must not exceed 255 characters' })
  withdrawalReason!: string;
}

export class ApplicationResponseDto {
  @ApiProperty({
    description: 'Application ID',
    example: '507f1f77bcf86cd799439011',
  })
  id!: string;

  @ApiProperty({
    description: 'Nurse information',
  })
  nurse!: {
    id: string;
    name: string;
    email: string;
    rating?: number;
    completedJobs?: number;
  };

  @ApiProperty({
    description: 'Request information',
  })
  request!: {
    id: string;
    title: string;
    description: string;
    serviceType: string;
    status: string;
    scheduledDate: Date;
    budget?: number;
  };

  @ApiProperty({
    description: 'Patient information',
  })
  patient!: {
    id: string;
    name: string;
    email: string;
  };

  @ApiProperty({
    description: 'Application status',
    enum: ApplicationStatus,
  })
  status!: ApplicationStatus;

  @ApiPropertyOptional({
    description: 'Cover letter from nurse',
  })
  coverLetter?: string;

  @ApiPropertyOptional({
    description: 'Proposed hourly rate',
  })
  proposedRate?: number;

  @ApiPropertyOptional({
    description: 'Estimated start time',
  })
  estimatedStartTime?: Date;

  @ApiPropertyOptional({
    description: 'Availability notes',
  })
  availabilityNotes?: string;

  @ApiProperty({
    description: 'Application submission date',
  })
  appliedAt!: Date;

  @ApiPropertyOptional({
    description: 'Response date',
  })
  respondedAt?: Date;

  @ApiPropertyOptional({
    description: 'Response notes',
  })
  responseNotes?: string;

  @ApiProperty({
    description: 'Application creation date',
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'Application last update date',
  })
  updatedAt!: Date;
}
