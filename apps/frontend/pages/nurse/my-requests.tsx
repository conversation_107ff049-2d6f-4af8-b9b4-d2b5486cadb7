import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../../lib/auth';
import { apiService } from '../../lib/api';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Fa<PERSON><PERSON><PERSON><PERSON>ist,
  FaMapMarkerAlt,
  FaClock,
  FaDollarSign,
  FaSpinner,
  FaRefresh,
  FaCheck,
  FaTimes,
  FaPlay,
  FaArrowLeft
} from 'react-icons/fa';

interface Application {
  id: string;
  status: string;
  coverLetter?: string;
  proposedRate?: number;
  appliedAt: string;
  respondedAt?: string;
  responseNotes?: string;
  request: {
    id: string;
    title: string;
    description: string;
    serviceType: string;
    status: string;
    scheduledDate: string;
    budget: number;
    address: string;
    patient: {
      id: string;
      name: string;
      email: string;
    };
  };
}

interface AssignedRequest {
  id: string;
  title: string;
  description: string;
  serviceType: string;
  status: string;
  address: string;
  scheduledDate: string;
  estimatedDuration: number;
  budget: number;
  acceptedAt?: string;
  completedAt?: string;
  cancelledAt?: string;
  patient: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
}

export default function MyRequests() {
  const [applications, setApplications] = useState<Application[]>([]);
  const [assignedRequests, setAssignedRequests] = useState<AssignedRequest[]>([]);
  const [activeTab, setActiveTab] = useState<'applications' | 'assigned'>('applications');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [updatingStatus, setUpdatingStatus] = useState<string | null>(null);

  const { user, token } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!user || user.role !== 'nurse') {
      router.push('/nurse/login');
      return;
    }

    fetchData();
  }, [user, router]);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const [applicationsData, requestsData] = await Promise.all([
        apiService.getMyApplications(),
        apiService.getMyRequests()
      ]);

      setApplications(applicationsData.applications || []);
      setAssignedRequests(requestsData.requests || []);
    } catch (err) {
      setError('Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateRequestStatus = async (requestId: string, status: string) => {
    setUpdatingStatus(requestId);
    try {
      await apiService.updateRequestStatus(requestId, status);
      // Refresh data
      fetchData();
    } catch (err: any) {
      setError(err.message || 'Failed to update request status');
    } finally {
      setUpdatingStatus(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'accepted':
        return 'text-green-600 bg-green-100';
      case 'rejected':
        return 'text-red-600 bg-red-100';
      case 'withdrawn':
        return 'text-gray-600 bg-gray-100';
      case 'in_progress':
        return 'text-blue-600 bg-blue-100';
      case 'work_in_progress':
        return 'text-purple-600 bg-purple-100';
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <FaSpinner className="animate-spin h-8 w-8 text-blue-600" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/nurse/dashboard')}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <FaArrowLeft className="h-5 w-5" />
              </button>
              <FaClipboardList className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">My Requests</h1>
                <p className="text-sm text-gray-600">Manage your applications and assigned requests</p>
              </div>
            </div>
            <button
              onClick={fetchData}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <FaRefresh className="h-4 w-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
            {error}
          </div>
        )}

        {/* Tabs */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('applications')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'applications'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              My Applications ({applications.length})
            </button>
            <button
              onClick={() => setActiveTab('assigned')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'assigned'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Assigned Requests ({assignedRequests.length})
            </button>
          </nav>
        </div>

        {/* Applications Tab */}
        {activeTab === 'applications' && (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">My Applications</h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                Track the status of your job applications
              </p>
            </div>
            <ul className="divide-y divide-gray-200">
              {applications.length === 0 ? (
                <li className="px-4 py-6 text-center text-gray-500">
                  You haven't applied to any requests yet.
                </li>
              ) : (
                applications.map((application) => (
                  <li key={application.id} className="px-4 py-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="text-lg font-medium text-gray-900">
                            {application.request.title}
                          </h4>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>
                            {application.status}
                          </span>
                        </div>
                        <p className="mt-1 text-sm text-gray-600">{application.request.description}</p>
                        <div className="mt-2 flex items-center text-sm text-gray-500 space-x-4">
                          <div className="flex items-center">
                            <FaMapMarkerAlt className="h-4 w-4 mr-1" />
                            {application.request.address}
                          </div>
                          <div className="flex items-center">
                            <FaClock className="h-4 w-4 mr-1" />
                            {formatDate(application.request.scheduledDate)}
                          </div>
                          <div className="flex items-center">
                            <FaDollarSign className="h-4 w-4 mr-1" />
                            EGP {application.request.budget}
                          </div>
                        </div>
                        <div className="mt-2 text-sm text-gray-600">
                          <span className="font-medium">Applied:</span> {formatDate(application.appliedAt)}
                          {application.respondedAt && (
                            <>
                              {' • '}
                              <span className="font-medium">Responded:</span> {formatDate(application.respondedAt)}
                            </>
                          )}
                        </div>
                        {application.responseNotes && (
                          <div className="mt-2 text-sm text-gray-600">
                            <span className="font-medium">Response:</span> {application.responseNotes}
                          </div>
                        )}
                      </div>
                    </div>
                  </li>
                ))
              )}
            </ul>
          </div>
        )}

        {/* Assigned Requests Tab */}
        {activeTab === 'assigned' && (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Assigned Requests</h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                Manage your active and completed requests
              </p>
            </div>
            <ul className="divide-y divide-gray-200">
              {assignedRequests.length === 0 ? (
                <li className="px-4 py-6 text-center text-gray-500">
                  You don't have any assigned requests yet.
                </li>
              ) : (
                assignedRequests.map((request) => (
                  <li key={request.id} className="px-4 py-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="text-lg font-medium text-gray-900">{request.title}</h4>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                            {request.status.replace('_', ' ')}
                          </span>
                        </div>
                        <p className="mt-1 text-sm text-gray-600">{request.description}</p>
                        <div className="mt-2 flex items-center text-sm text-gray-500 space-x-4">
                          <div className="flex items-center">
                            <FaMapMarkerAlt className="h-4 w-4 mr-1" />
                            {request.address}
                          </div>
                          <div className="flex items-center">
                            <FaClock className="h-4 w-4 mr-1" />
                            {formatDate(request.scheduledDate)}
                          </div>
                          <div className="flex items-center">
                            <FaDollarSign className="h-4 w-4 mr-1" />
                            EGP {request.budget}
                          </div>
                        </div>
                        <div className="mt-2 text-sm text-gray-600">
                          <span className="font-medium">Patient:</span> {request.patient.name} ({request.patient.phone})
                        </div>
                      </div>
                      <div className="ml-6 flex space-x-2">
                        {request.status === 'in_progress' && (
                          <button
                            onClick={() => handleUpdateRequestStatus(request.id, 'work_in_progress')}
                            disabled={updatingStatus === request.id}
                            className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50"
                          >
                            {updatingStatus === request.id ? (
                              <FaSpinner className="animate-spin h-4 w-4" />
                            ) : (
                              <>
                                <FaPlay className="h-4 w-4 mr-1" />
                                Start Work
                              </>
                            )}
                          </button>
                        )}
                        {request.status === 'work_in_progress' && (
                          <button
                            onClick={() => handleUpdateRequestStatus(request.id, 'completed')}
                            disabled={updatingStatus === request.id}
                            className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                          >
                            {updatingStatus === request.id ? (
                              <FaSpinner className="animate-spin h-4 w-4" />
                            ) : (
                              <>
                                <FaCheck className="h-4 w-4 mr-1" />
                                Complete
                              </>
                            )}
                          </button>
                        )}
                        {(request.status === 'in_progress' || request.status === 'work_in_progress') && (
                          <button
                            onClick={() => handleUpdateRequestStatus(request.id, 'cancelled')}
                            disabled={updatingStatus === request.id}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                          >
                            {updatingStatus === request.id ? (
                              <FaSpinner className="animate-spin h-4 w-4" />
                            ) : (
                              <>
                                <FaTimes className="h-4 w-4 mr-1" />
                                Cancel
                              </>
                            )}
                          </button>
                        )}
                      </div>
                    </div>
                  </li>
                ))
              )}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
