import { useState, useEffect, useC<PERSON>back, useMemo, memo } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../../lib/auth';
import { apiService } from '../../lib/api';
import '../../styles/nurse-dashboard.css';
import {
  FaUserNurse,
  FaClipboardList,
  FaMapMarkerAlt,
  FaClock,
  FaDollarSign,
  FaEye,
  FaSpinner,
  FaRefresh,
  FaPhone,
  FaExclamationTriangle,
  FaExclamationCircle,
  FaInfoCircle,
  FaCalendarAlt,
  FaUser,
  FaCheckSquare,
  FaSquare,
  FaTimes,
  FaCheck,
  FaEdit,
  FaPlay,
  FaStop,
  FaPause,
  FaHistory,
  FaBell,
  FaFilter,
  FaSort,
  FaRoute,
  FaCertificate,
  FaHeart,
  FaStar,
  FaArrowRight,
  FaChevronDown,
  FaChevronUp,
  FaSearch,
  FaSliders,
  FaMapPin,
  FaStethoscope,
  FaAward,
  FaCheckCircle,
  FaTimesCircle,
  FaExclamation,
  FaClock as FaClockSolid,
  FaEllipsisV
} from 'react-icons/fa';

// Healthcare Design System Colors
const colors = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    900: '#1e3a8a'
  },
  secondary: {
    50: '#ecfdf5',
    100: '#d1fae5',
    500: '#10b981',
    600: '#059669',
    700: '#047857'
  },
  status: {
    urgent: '#dc2626',
    medium: '#f59e0b',
    low: '#10b981',
    pending: '#6b7280',
    inProgress: '#3b82f6',
    completed: '#10b981',
    cancelled: '#dc2626'
  }
};

interface PatientRequest {
  id: string;
  title: string;
  description: string;
  serviceType: string;
  address: string;
  location: string | { type: string; coordinates: [number, number] };
  scheduledDate: string;
  estimatedDuration: number;
  urgencyLevel: 'low' | 'medium' | 'high';
  specialRequirements?: string;
  budget: number;
  contactPhone?: string;
  notes?: string;
  status?: 'pending' | 'in_progress' | 'work_in_progress' | 'completed' | 'cancelled';
  requiredCertifications?: string[];
  medicalHistory?: string;
  distance?: number;
  travelTime?: number;
  compatibilityScore?: number;
  patient: {
    id: string;
    name: string;
    email: string;
    address?: string;
    age?: number;
    medicalConditions?: string[];
  };
  createdAt: string;
}

interface Application {
  id: string;
  requestId: string;
  nurseId: string;
  status: 'pending' | 'under_review' | 'accepted' | 'rejected' | 'withdrawn';
  coverLetter: string;
  proposedRate: number;
  estimatedStartTime: string;
  availabilityNotes: string;
  appliedAt: string;
  statusHistory: {
    status: string;
    timestamp: string;
    notes?: string;
  }[];
}

interface ApplicationFormData {
  coverLetter: string;
  proposedRate: number;
  estimatedStartTime: string;
  availabilityNotes: string;
}

interface FilterOptions {
  serviceTypes: string[];
  urgencyLevels: string[];
  budgetRange: [number, number];
  dateRange: [string, string];
  maxDistance: number;
  requiredCertifications: string[];
}

interface SortOption {
  field: 'date' | 'budget' | 'distance' | 'urgency' | 'compatibility';
  direction: 'asc' | 'desc';
}

interface DashboardStats {
  applications: {
    total: number;
    pending: number;
    accepted: number;
    rejected: number;
  };
  requests: {
    total: number;
    inProgress: number;
    completed: number;
    cancelled: number;
  };
  earnings: {
    estimated: number;
    hourlyRate: number;
    completedJobs: number;
  };
  profile: {
    rating: number;
    totalReviews: number;
    isAvailable: boolean;
  };
}

// Memoized Request Card Component for better performance
const RequestCard = memo(({ request, selectedRequests, toggleRequestSelection, openApplicationModal, handleApplyToRequest, applyingToRequest, openStatusChangeModal }: {
  request: PatientRequest;
  selectedRequests: Set<string>;
  toggleRequestSelection: (id: string) => void;
  openApplicationModal: (id: string) => void;
  handleApplyToRequest: (id: string) => void;
  applyingToRequest: string | null;
  openStatusChangeModal: (requestId: string, newStatus: string) => void;
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency.toLowerCase()) {
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'medium':
        return 'text-amber-600 bg-amber-50 border-amber-200';
      case 'low':
        return 'text-emerald-600 bg-emerald-50 border-emerald-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency.toLowerCase()) {
      case 'high':
        return <FaExclamationTriangle className="h-4 w-4" />;
      case 'medium':
        return <FaExclamationCircle className="h-4 w-4" />;
      case 'low':
        return <FaInfoCircle className="h-4 w-4" />;
      default:
        return <FaInfoCircle className="h-4 w-4" />;
    }
  };

  const getServiceTypeColor = (serviceType: string) => {
    const colors = {
      'home_care': 'bg-blue-50 text-blue-700 border-blue-200',
      'elderly_care': 'bg-purple-50 text-purple-700 border-purple-200',
      'post_surgery': 'bg-red-50 text-red-700 border-red-200',
      'chronic_care': 'bg-orange-50 text-orange-700 border-orange-200',
      'rehabilitation': 'bg-green-50 text-green-700 border-green-200',
      'palliative_care': 'bg-gray-50 text-gray-700 border-gray-200',
      'pediatric_care': 'bg-pink-50 text-pink-700 border-pink-200',
      'mental_health': 'bg-indigo-50 text-indigo-700 border-indigo-200',
    };
    return colors[serviceType as keyof typeof colors] || 'bg-gray-50 text-gray-700 border-gray-200';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'in_progress':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'work_in_progress':
        return 'text-indigo-600 bg-indigo-50 border-indigo-200';
      case 'completed':
        return 'text-emerald-600 bg-emerald-50 border-emerald-200';
      case 'cancelled':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getCompatibilityColor = (score: number) => {
    if (score >= 90) return 'text-emerald-600 bg-emerald-50';
    if (score >= 75) return 'text-blue-600 bg-blue-50';
    if (score >= 60) return 'text-amber-600 bg-amber-50';
    return 'text-gray-600 bg-gray-50';
  };

  return (
    <div className={`bg-white border border-slate-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden card-hover ${
      selectedRequests.has(request.id) ? 'ring-2 ring-blue-500 border-blue-500' : ''
    }`}>
      {/* Request content would go here - truncated for brevity */}
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-start space-x-3 flex-1">
            <button
              onClick={() => toggleRequestSelection(request.id)}
              className="mt-1 text-slate-400 hover:text-blue-600 transition-colors duration-200 focus-ring"
              aria-label={selectedRequests.has(request.id) ? 'Deselect request' : 'Select request'}
            >
              {selectedRequests.has(request.id) ? (
                <FaCheckSquare className="h-5 w-5 text-blue-600" />
              ) : (
                <FaSquare className="h-5 w-5" />
              )}
            </button>
            <div className="flex-1">
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="text-lg font-bold text-slate-900 mb-2 leading-tight">{request.title}</h4>
                  <div className="flex items-center space-x-2 mb-3">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getUrgencyColor(request.urgencyLevel)}`}>
                      {getUrgencyIcon(request.urgencyLevel)}
                      <span className="ml-1 capitalize">{request.urgencyLevel}</span>
                    </span>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getServiceTypeColor(request.serviceType)}`}>
                      <FaStethoscope className="h-3 w-3 mr-1" />
                      {request.serviceType.replace('_', ' ')}
                    </span>
                  </div>
                </div>
                <div className={`flex items-center space-x-1 px-2 py-1 rounded-lg text-xs font-medium ${getCompatibilityColor(request.compatibilityScore || 0)}`}>
                  <FaHeart className="h-3 w-3" />
                  <span>{request.compatibilityScore || 0}% match</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <p className="text-sm text-slate-600 mb-4 leading-relaxed">{request.description}</p>
        {/* Additional content would be here */}
      </div>
    </div>
  );
});

RequestCard.displayName = 'RequestCard';

function NurseDashboard() {
  // Core state
  const [availableRequests, setAvailableRequests] = useState<PatientRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<PatientRequest[]>([]);
  const [myApplications, setMyApplications] = useState<Application[]>([]);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [notifications, setNotifications] = useState<string[]>([]);

  // Application state
  const [applyingToRequest, setApplyingToRequest] = useState<string | null>(null);
  const [selectedRequests, setSelectedRequests] = useState<Set<string>>(new Set());
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [currentRequestId, setCurrentRequestId] = useState<string | null>(null);
  const [showBulkApplicationModal, setShowBulkApplicationModal] = useState(false);
  const [applicationStep, setApplicationStep] = useState(1);
  const [applicationForm, setApplicationForm] = useState<ApplicationFormData>({
    coverLetter: '',
    proposedRate: 150,
    estimatedStartTime: '',
    availabilityNotes: ''
  });
  const [bulkApplicationForm, setBulkApplicationForm] = useState<ApplicationFormData>({
    coverLetter: '',
    proposedRate: 150,
    estimatedStartTime: '',
    availabilityNotes: ''
  });
  const [bulkApplicationResults, setBulkApplicationResults] = useState<{[key: string]: {success: boolean, error?: string}}>({});
  const [isBulkApplying, setIsBulkApplying] = useState(false);

  // UI state
  const [activeTab, setActiveTab] = useState<'available' | 'applications' | 'active'>('available');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    serviceTypes: [],
    urgencyLevels: [],
    budgetRange: [0, 1000],
    dateRange: ['', ''],
    maxDistance: 50,
    requiredCertifications: []
  });
  const [sortOption, setSortOption] = useState<SortOption>({
    field: 'date',
    direction: 'desc'
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Status management
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [statusChangeRequest, setStatusChangeRequest] = useState<{requestId: string, newStatus: string} | null>(null);
  const [statusChangeReason, setStatusChangeReason] = useState('');

  const { user, token } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!user || user.role !== 'nurse') {
      router.push('/nurse/login');
      return;
    }

    fetchDashboardData();
  }, [user, router]);

  // Enhanced data fetching with real-time capabilities
  const fetchDashboardData = useCallback(async () => {
    setIsLoading(true);
    try {
      // Fetch available requests
      const requestsResponse = await fetch('http://localhost:3001/api/requests/available');
      const requestsData = await requestsResponse.json();

      // Enhanced requests with calculated fields
      const enhancedRequests = (requestsData.data?.requests || []).map((request: PatientRequest) => ({
        ...request,
        distance: calculateDistance(request),
        travelTime: calculateTravelTime(request),
        compatibilityScore: calculateCompatibilityScore(request),
        requiredCertifications: extractRequiredCertifications(request),
        medicalHistory: generateMedicalHistorySummary(request)
      }));

      setAvailableRequests(enhancedRequests);

      // Mock enhanced stats data
      const mockStats = {
        applications: { total: 12, pending: 3, accepted: 7, rejected: 2 },
        requests: { total: 8, inProgress: 2, completed: 5, cancelled: 1 },
        earnings: { estimated: 2400, hourlyRate: 150, completedJobs: 16 },
        profile: { rating: 4.8, totalReviews: 24, isAvailable: true }
      };

      setStats(mockStats);

      // Mock applications data
      const mockApplications: Application[] = [
        {
          id: '1',
          requestId: 'req1',
          nurseId: user?.id || '',
          status: 'pending',
          coverLetter: 'Sample cover letter',
          proposedRate: 150,
          estimatedStartTime: new Date().toISOString(),
          availabilityNotes: 'Available immediately',
          appliedAt: new Date().toISOString(),
          statusHistory: [
            { status: 'pending', timestamp: new Date().toISOString() }
          ]
        }
      ];

      setMyApplications(mockApplications);
    } catch (err) {
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // Utility functions for enhanced request processing
  const calculateDistance = (request: PatientRequest): number => {
    // Mock distance calculation - in real app, use geolocation API
    return Math.floor(Math.random() * 25) + 1;
  };

  const calculateTravelTime = (request: PatientRequest): number => {
    // Mock travel time calculation
    const distance = calculateDistance(request);
    return Math.ceil(distance * 2.5); // Assume 2.5 minutes per km
  };

  const calculateCompatibilityScore = (request: PatientRequest): number => {
    // Mock compatibility scoring based on nurse qualifications
    let score = 70; // Base score

    if (request.urgencyLevel === 'low') score += 10;
    if (request.serviceType === 'elderly_care') score += 15;
    if (request.budget >= 150) score += 5;

    return Math.min(score, 100);
  };

  const extractRequiredCertifications = (request: PatientRequest): string[] => {
    const certifications = [];
    if (request.serviceType === 'post_surgery') certifications.push('Post-Operative Care');
    if (request.serviceType === 'pediatric_care') certifications.push('Pediatric Nursing');
    if (request.serviceType === 'mental_health') certifications.push('Mental Health First Aid');
    if (request.urgencyLevel === 'high') certifications.push('Emergency Care');
    return certifications;
  };

  const generateMedicalHistorySummary = (request: PatientRequest): string => {
    // Mock medical history based on service type
    const summaries = {
      'post_surgery': 'Recent orthopedic surgery, requires wound care and mobility assistance',
      'elderly_care': 'Diabetes management, hypertension, requires medication monitoring',
      'pediatric_care': 'Respiratory condition, requires specialized pediatric care',
      'chronic_care': 'Chronic pain management, mobility limitations',
      'mental_health': 'Depression recovery, medication compliance monitoring',
      'rehabilitation': 'Stroke recovery, speech and mobility therapy needed'
    };
    return summaries[request.serviceType as keyof typeof summaries] || 'Standard nursing care required';
  };

  const handleApplyToRequest = async (requestId: string, formData?: ApplicationFormData) => {
    setApplyingToRequest(requestId);
    try {
      const applicationData = formData || {
        coverLetter: 'I am interested in this position and believe I can provide excellent care.',
        proposedRate: 150,
        estimatedStartTime: '',
        availabilityNotes: ''
      };

      // For demo purposes, simulate successful application
      console.log('Applying to request:', requestId, 'with data:', applicationData);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Remove the request from available requests to simulate successful application
      setAvailableRequests(prev => prev.filter(req => req.id !== requestId));

      // Close modal and reset form
      setShowApplicationModal(false);
      setCurrentRequestId(null);
      resetApplicationForm();

      // Show success message
      setError('');
      alert('Application submitted successfully!');
    } catch (err: any) {
      setError(err.message || 'Failed to apply to request');
    } finally {
      setApplyingToRequest(null);
    }
  };

  const handleBulkApplication = async () => {
    setIsBulkApplying(true);
    const results: {[key: string]: {success: boolean, error?: string}} = {};

    // Simulate bulk application process
    for (const requestId of selectedRequests) {
      try {
        console.log('Bulk applying to request:', requestId, 'with data:', bulkApplicationForm);

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        results[requestId] = { success: true };
        // Remove from available requests
        setAvailableRequests(prev => prev.filter(req => req.id !== requestId));
      } catch (err: any) {
        results[requestId] = { success: false, error: err.message };
      }
    }

    setBulkApplicationResults(results);
    setSelectedRequests(new Set());
    setIsBulkApplying(false);

    // Close modal after a delay to show results
    setTimeout(() => {
      setShowBulkApplicationModal(false);
      setBulkApplicationResults({});
      resetBulkApplicationForm();
    }, 3000);
  };

  const resetApplicationForm = () => {
    setApplicationForm({
      coverLetter: '',
      proposedRate: 150,
      estimatedStartTime: '',
      availabilityNotes: ''
    });
  };

  const resetBulkApplicationForm = () => {
    setBulkApplicationForm({
      coverLetter: '',
      proposedRate: 150,
      estimatedStartTime: '',
      availabilityNotes: ''
    });
  };

  const toggleRequestSelection = (requestId: string) => {
    const newSelected = new Set(selectedRequests);
    if (newSelected.has(requestId)) {
      newSelected.delete(requestId);
    } else {
      newSelected.add(requestId);
    }
    setSelectedRequests(newSelected);
  };

  const openApplicationModal = (requestId: string) => {
    setCurrentRequestId(requestId);
    setShowApplicationModal(true);
  };

  const openBulkApplicationModal = () => {
    setShowBulkApplicationModal(true);
  };

  // Status workflow management
  const handleStatusChange = async (requestId: string, newStatus: string, reason?: string) => {
    try {
      console.log('Changing status for request:', requestId, 'to:', newStatus, 'reason:', reason);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update local state
      setAvailableRequests(prev => prev.map(req =>
        req.id === requestId ? { ...req, status: newStatus as any } : req
      ));

      // Add notification
      setNotifications(prev => [...prev, `Request status updated to ${newStatus}`]);

      // Close modal
      setShowStatusModal(false);
      setStatusChangeRequest(null);
      setStatusChangeReason('');

    } catch (error) {
      setError('Failed to update request status');
    }
  };

  const openStatusChangeModal = (requestId: string, newStatus: string) => {
    setStatusChangeRequest({ requestId, newStatus });
    setShowStatusModal(true);
  };

  // Real-time features
  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      // Mock new notification
      if (Math.random() > 0.95) {
        setNotifications(prev => [...prev, 'New request matching your skills available!']);
      }
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  // Auto-refresh data
  useEffect(() => {
    const interval = setInterval(() => {
      fetchDashboardData();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [fetchDashboardData]);

  // Advanced filtering and sorting
  const applyFiltersAndSort = useCallback(() => {
    let filtered = [...availableRequests];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(request =>
        request.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        request.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        request.patient.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply service type filter
    if (filters.serviceTypes.length > 0) {
      filtered = filtered.filter(request =>
        filters.serviceTypes.includes(request.serviceType)
      );
    }

    // Apply urgency filter
    if (filters.urgencyLevels.length > 0) {
      filtered = filtered.filter(request =>
        filters.urgencyLevels.includes(request.urgencyLevel)
      );
    }

    // Apply budget range filter
    filtered = filtered.filter(request =>
      request.budget >= filters.budgetRange[0] && request.budget <= filters.budgetRange[1]
    );

    // Apply distance filter
    filtered = filtered.filter(request =>
      (request.distance || 0) <= filters.maxDistance
    );

    // Apply date range filter
    if (filters.dateRange[0] && filters.dateRange[1]) {
      const startDate = new Date(filters.dateRange[0]);
      const endDate = new Date(filters.dateRange[1]);
      filtered = filtered.filter(request => {
        const requestDate = new Date(request.scheduledDate);
        return requestDate >= startDate && requestDate <= endDate;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortOption.field) {
        case 'date':
          aValue = new Date(a.scheduledDate).getTime();
          bValue = new Date(b.scheduledDate).getTime();
          break;
        case 'budget':
          aValue = a.budget;
          bValue = b.budget;
          break;
        case 'distance':
          aValue = a.distance || 0;
          bValue = b.distance || 0;
          break;
        case 'urgency':
          const urgencyOrder = { high: 3, medium: 2, low: 1 };
          aValue = urgencyOrder[a.urgencyLevel as keyof typeof urgencyOrder];
          bValue = urgencyOrder[b.urgencyLevel as keyof typeof urgencyOrder];
          break;
        case 'compatibility':
          aValue = a.compatibilityScore || 0;
          bValue = b.compatibilityScore || 0;
          break;
        default:
          aValue = 0;
          bValue = 0;
      }

      if (sortOption.direction === 'asc') {
        return aValue - bValue;
      } else {
        return bValue - aValue;
      }
    });

    setFilteredRequests(filtered);
  }, [availableRequests, searchQuery, filters, sortOption]);

  // Apply filters whenever dependencies change
  useEffect(() => {
    applyFiltersAndSort();
  }, [applyFiltersAndSort]);

  // Memoized filtered and sorted requests for performance
  const memoizedFilteredRequests = useMemo(() => filteredRequests, [filteredRequests]);

  // Memoized stats for performance
  const memoizedStats = useMemo(() => stats, [stats]);

  // Utility functions for UI
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDateOnly = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency.toLowerCase()) {
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'medium':
        return 'text-amber-600 bg-amber-50 border-amber-200';
      case 'low':
        return 'text-emerald-600 bg-emerald-50 border-emerald-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency.toLowerCase()) {
      case 'high':
        return <FaExclamationTriangle className="h-4 w-4" />;
      case 'medium':
        return <FaExclamationCircle className="h-4 w-4" />;
      case 'low':
        return <FaInfoCircle className="h-4 w-4" />;
      default:
        return <FaInfoCircle className="h-4 w-4" />;
    }
  };

  const getServiceTypeColor = (serviceType: string) => {
    const colors = {
      'home_care': 'bg-blue-50 text-blue-700 border-blue-200',
      'elderly_care': 'bg-purple-50 text-purple-700 border-purple-200',
      'post_surgery': 'bg-red-50 text-red-700 border-red-200',
      'chronic_care': 'bg-orange-50 text-orange-700 border-orange-200',
      'rehabilitation': 'bg-green-50 text-green-700 border-green-200',
      'palliative_care': 'bg-gray-50 text-gray-700 border-gray-200',
      'pediatric_care': 'bg-pink-50 text-pink-700 border-pink-200',
      'mental_health': 'bg-indigo-50 text-indigo-700 border-indigo-200',
    };
    return colors[serviceType as keyof typeof colors] || 'bg-gray-50 text-gray-700 border-gray-200';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'in_progress':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'work_in_progress':
        return 'text-indigo-600 bg-indigo-50 border-indigo-200';
      case 'completed':
        return 'text-emerald-600 bg-emerald-50 border-emerald-200';
      case 'cancelled':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getCompatibilityColor = (score: number) => {
    if (score >= 90) return 'text-emerald-600 bg-emerald-50';
    if (score >= 75) return 'text-blue-600 bg-blue-50';
    if (score >= 60) return 'text-amber-600 bg-amber-50';
    return 'text-gray-600 bg-gray-50';
  };

  // Premium Loading State
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-lg mb-4">
              <FaSpinner className="animate-spin h-8 w-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-medium text-slate-900 mb-2">Loading Dashboard</h3>
            <p className="text-sm text-slate-600">Fetching your latest requests and applications...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Premium Header */}
      <div className="bg-white shadow-lg border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-lg mr-4">
                <FaStethoscope className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-slate-900 tracking-tight">Nurse Dashboard</h1>
                <p className="text-sm text-slate-600 font-medium">Welcome back, {user?.name || 'Nurse'}</p>
              </div>
            </div>

            {/* Header Actions */}
            <div className="flex items-center space-x-3">
              {/* Notifications */}
              <div className="relative">
                <button className="relative p-2 text-slate-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200">
                  <FaBell className="h-5 w-5" />
                  {notifications.length > 0 && (
                    <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                      {notifications.length}
                    </span>
                  )}
                </button>
              </div>

              {/* Refresh Button */}
              <button
                onClick={fetchDashboardData}
                disabled={isLoading}
                className="inline-flex items-center px-3 py-2 border border-slate-300 shadow-sm text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 disabled:opacity-50"
              >
                <FaRefresh className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </button>

              {/* My Requests Button */}
              <button
                onClick={() => router.push('/nurse/my-requests')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg transition-all duration-200 transform hover:scale-105"
              >
                <FaClipboardList className="h-4 w-4 mr-2" />
                My Requests
              </button>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="border-t border-slate-200">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              {[
                { id: 'available', name: 'Available Requests', icon: FaSearch, count: filteredRequests.length },
                { id: 'applications', name: 'My Applications', icon: FaClipboardList, count: myApplications.length },
                { id: 'active', name: 'Active Work', icon: FaPlay, count: 2 }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 bg-blue-50'
                      : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                  } whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm rounded-t-lg transition-all duration-200 flex items-center space-x-2`}
                >
                  <tab.icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                  <span className={`${
                    activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'bg-slate-100 text-slate-600'
                  } inline-flex items-center justify-center px-2 py-1 rounded-full text-xs font-medium`}>
                    {tab.count}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Error Alert */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-xl p-4 shadow-sm">
            <div className="flex items-center">
              <FaExclamationTriangle className="h-5 w-5 text-red-600 mr-3" />
              <p className="text-red-700 font-medium">{error}</p>
            </div>
          </div>
        )}

        {/* Premium Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Applications Card */}
            <div className="bg-white overflow-hidden shadow-lg rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600 mb-1">Applications</p>
                    <p className="text-2xl font-bold text-slate-900">{stats.applications.total}</p>
                    <div className="flex items-center mt-2 space-x-4 text-xs">
                      <span className="text-amber-600">Pending: {stats.applications.pending}</span>
                      <span className="text-emerald-600">Accepted: {stats.applications.accepted}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl">
                    <FaClipboardList className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Active Requests Card */}
            <div className="bg-white overflow-hidden shadow-lg rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600 mb-1">Active Work</p>
                    <p className="text-2xl font-bold text-slate-900">{stats.requests.inProgress}</p>
                    <div className="flex items-center mt-2 space-x-4 text-xs">
                      <span className="text-blue-600">In Progress: {stats.requests.inProgress}</span>
                      <span className="text-emerald-600">Completed: {stats.requests.completed}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-emerald-100 rounded-xl">
                    <FaUserNurse className="h-6 w-6 text-emerald-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Earnings Card */}
            <div className="bg-white overflow-hidden shadow-lg rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600 mb-1">Estimated Earnings</p>
                    <p className="text-2xl font-bold text-slate-900">EGP {stats.earnings.estimated.toLocaleString()}</p>
                    <div className="flex items-center mt-2 space-x-4 text-xs">
                      <span className="text-slate-600">Rate: EGP {stats.earnings.hourlyRate}/hr</span>
                      <span className="text-emerald-600">Jobs: {stats.earnings.completedJobs}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-amber-100 rounded-xl">
                    <FaDollarSign className="h-6 w-6 text-amber-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Rating Card */}
            <div className="bg-white overflow-hidden shadow-lg rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600 mb-1">Rating</p>
                    <div className="flex items-center space-x-2">
                      <p className="text-2xl font-bold text-slate-900">{stats.profile.rating}</p>
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <FaStar
                            key={i}
                            className={`h-4 w-4 ${
                              i < Math.floor(stats.profile.rating) ? 'text-amber-400' : 'text-slate-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    <p className="text-xs text-slate-600 mt-2">{stats.profile.totalReviews} reviews</p>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl">
                    <FaAward className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tab Content */}
        {activeTab === 'available' && (
          <div className="bg-white shadow-xl rounded-2xl border border-slate-200 overflow-hidden">
            {/* Advanced Search and Filter Header */}
            <div className="px-6 py-6 border-b border-slate-200 bg-gradient-to-r from-slate-50 to-blue-50">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div>
                  <h3 className="text-xl font-bold text-slate-900 mb-1">Available Patient Requests</h3>
                  <p className="text-sm text-slate-600">
                    Find and apply to requests that match your skills and availability
                  </p>
                </div>

                {/* Search and Filter Controls */}
                <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
                  {/* Search Bar */}
                  <div className="relative">
                    <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                    <input
                      type="text"
                      placeholder="Search requests..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm w-full sm:w-64"
                    />
                  </div>

                  {/* Filter Toggle */}
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className={`inline-flex items-center px-4 py-2 border text-sm font-medium rounded-lg transition-all duration-200 ${
                      showFilters
                        ? 'border-blue-500 text-blue-600 bg-blue-50'
                        : 'border-slate-300 text-slate-700 bg-white hover:bg-slate-50'
                    }`}
                  >
                    <FaSliders className="h-4 w-4 mr-2" />
                    Filters
                    {showFilters ? <FaChevronUp className="h-3 w-3 ml-2" /> : <FaChevronDown className="h-3 w-3 ml-2" />}
                  </button>

                  {/* Sort Dropdown */}
                  <select
                    value={`${sortOption.field}-${sortOption.direction}`}
                    onChange={(e) => {
                      const [field, direction] = e.target.value.split('-');
                      setSortOption({ field: field as any, direction: direction as any });
                    }}
                    className="px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
                  >
                    <option value="date-desc">Latest First</option>
                    <option value="date-asc">Oldest First</option>
                    <option value="budget-desc">Highest Budget</option>
                    <option value="budget-asc">Lowest Budget</option>
                    <option value="distance-asc">Nearest First</option>
                    <option value="urgency-desc">Most Urgent</option>
                    <option value="compatibility-desc">Best Match</option>
                  </select>

                  {/* View Mode Toggle */}
                  <div className="flex items-center border border-slate-300 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm font-medium transition-colors ${
                        viewMode === 'grid'
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-slate-600 hover:bg-slate-50'
                      }`}
                    >
                      Grid
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm font-medium transition-colors ${
                        viewMode === 'list'
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-slate-600 hover:bg-slate-50'
                      }`}
                    >
                      List
                    </button>
                  </div>
                </div>
              </div>

              {/* Bulk Actions */}
              {selectedRequests.size > 0 && (
                <div className="mt-4 flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <FaCheckCircle className="h-5 w-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">
                      {selectedRequests.size} request{selectedRequests.size > 1 ? 's' : ''} selected
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={openBulkApplicationModal}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-emerald-600 hover:bg-emerald-700 shadow-sm transition-all duration-200"
                    >
                      <FaCheck className="h-4 w-4 mr-2" />
                      Apply to Selected
                    </button>
                    <button
                      onClick={() => setSelectedRequests(new Set())}
                      className="inline-flex items-center px-3 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-all duration-200"
                    >
                      <FaTimes className="h-4 w-4 mr-2" />
                      Clear Selection
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Advanced Filters Panel */}
            {showFilters && (
              <div className="px-6 py-6 border-b border-slate-200 bg-slate-50">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {/* Service Types */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">Service Types</label>
                    <div className="space-y-2">
                      {['elderly_care', 'post_surgery', 'pediatric_care', 'chronic_care', 'mental_health', 'rehabilitation'].map((type) => (
                        <label key={type} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={filters.serviceTypes.includes(type)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilters(prev => ({
                                  ...prev,
                                  serviceTypes: [...prev.serviceTypes, type]
                                }));
                              } else {
                                setFilters(prev => ({
                                  ...prev,
                                  serviceTypes: prev.serviceTypes.filter(t => t !== type)
                                }));
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                          />
                          <span className="ml-2 text-sm text-slate-600 capitalize">
                            {type.replace('_', ' ')}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Urgency Levels */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">Urgency Level</label>
                    <div className="space-y-2">
                      {['high', 'medium', 'low'].map((level) => (
                        <label key={level} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={filters.urgencyLevels.includes(level)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilters(prev => ({
                                  ...prev,
                                  urgencyLevels: [...prev.urgencyLevels, level]
                                }));
                              } else {
                                setFilters(prev => ({
                                  ...prev,
                                  urgencyLevels: prev.urgencyLevels.filter(l => l !== level)
                                }));
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                          />
                          <span className={`ml-2 text-sm capitalize ${getUrgencyColor(level).split(' ')[0]}`}>
                            {level}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Budget Range */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">Budget Range (EGP)</label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          placeholder="Min"
                          value={filters.budgetRange[0]}
                          onChange={(e) => setFilters(prev => ({
                            ...prev,
                            budgetRange: [parseInt(e.target.value) || 0, prev.budgetRange[1]]
                          }))}
                          className="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500"
                        />
                        <span className="text-slate-500">-</span>
                        <input
                          type="number"
                          placeholder="Max"
                          value={filters.budgetRange[1]}
                          onChange={(e) => setFilters(prev => ({
                            ...prev,
                            budgetRange: [prev.budgetRange[0], parseInt(e.target.value) || 1000]
                          }))}
                          className="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Distance */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Max Distance: {filters.maxDistance} km
                    </label>
                    <input
                      type="range"
                      min="1"
                      max="100"
                      value={filters.maxDistance}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        maxDistance: parseInt(e.target.value)
                      }))}
                      className="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-xs text-slate-500 mt-1">
                      <span>1 km</span>
                      <span>100 km</span>
                    </div>
                  </div>
                </div>

                {/* Filter Actions */}
                <div className="flex items-center justify-between mt-6 pt-4 border-t border-slate-200">
                  <button
                    onClick={() => {
                      setFilters({
                        serviceTypes: [],
                        urgencyLevels: [],
                        budgetRange: [0, 1000],
                        dateRange: ['', ''],
                        maxDistance: 50,
                        requiredCertifications: []
                      });
                      setSearchQuery('');
                    }}
                    className="text-sm text-slate-600 hover:text-slate-900 font-medium"
                  >
                    Clear All Filters
                  </button>
                  <div className="text-sm text-slate-600">
                    Showing {filteredRequests.length} of {availableRequests.length} requests
                  </div>
                </div>
              </div>
            )}

          {/* Request Content */}
          {filteredRequests.length === 0 ? (
            <div className="px-6 py-16 text-center">
              <div className="mx-auto w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4">
                <FaSearch className="h-8 w-8 text-slate-400" />
              </div>
              <h3 className="text-lg font-medium text-slate-900 mb-2">No requests found</h3>
              <p className="text-sm text-slate-600 mb-6 max-w-md mx-auto">
                {searchQuery || filters.serviceTypes.length > 0 || filters.urgencyLevels.length > 0
                  ? 'Try adjusting your search criteria or filters to find more requests.'
                  : 'Check back later for new patient requests that match your skills.'}
              </p>
              {(searchQuery || filters.serviceTypes.length > 0 || filters.urgencyLevels.length > 0) && (
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setFilters({
                      serviceTypes: [],
                      urgencyLevels: [],
                      budgetRange: [0, 1000],
                      dateRange: ['', ''],
                      maxDistance: 50,
                      requiredCertifications: []
                    });
                  }}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700"
                >
                  Clear Filters
                </button>
              )}
            </div>
          ) : (
            <div className={`p-6 ${viewMode === 'grid' ? 'grid grid-cols-1 lg:grid-cols-2 gap-6' : 'space-y-4'}`}>
              {filteredRequests.map((request) => (
                <div key={request.id} className={`bg-white border border-slate-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden ${
                  selectedRequests.has(request.id) ? 'ring-2 ring-blue-500 border-blue-500' : ''
                }`}>
                  {/* Request Header */}
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start space-x-3 flex-1">
                        <button
                          onClick={() => toggleRequestSelection(request.id)}
                          className="mt-1 text-slate-400 hover:text-blue-600 transition-colors duration-200"
                          aria-label={selectedRequests.has(request.id) ? 'Deselect request' : 'Select request'}
                        >
                          {selectedRequests.has(request.id) ? (
                            <FaCheckSquare className="h-5 w-5 text-blue-600" />
                          ) : (
                            <FaSquare className="h-5 w-5" />
                          )}
                        </button>
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="text-lg font-bold text-slate-900 mb-2 leading-tight">{request.title}</h4>
                              <div className="flex items-center space-x-2 mb-3">
                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getUrgencyColor(request.urgencyLevel)}`}>
                                  {getUrgencyIcon(request.urgencyLevel)}
                                  <span className="ml-1 capitalize">{request.urgencyLevel}</span>
                                </span>
                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getServiceTypeColor(request.serviceType)}`}>
                                  <FaStethoscope className="h-3 w-3 mr-1" />
                                  {request.serviceType.replace('_', ' ')}
                                </span>
                              </div>
                            </div>
                            {/* Compatibility Score */}
                            <div className={`flex items-center space-x-1 px-2 py-1 rounded-lg text-xs font-medium ${getCompatibilityColor(request.compatibilityScore || 0)}`}>
                              <FaHeart className="h-3 w-3" />
                              <span>{request.compatibilityScore || 0}% match</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Request Description */}
                    <p className="text-sm text-slate-600 mb-4 leading-relaxed">{request.description}</p>

                    {/* Patient Information Card */}
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4 mb-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                            <FaUser className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <h5 className="text-sm font-semibold text-slate-900">{request.patient.name}</h5>
                            {request.patient.age && (
                              <p className="text-xs text-slate-600">Age: {request.patient.age}</p>
                            )}
                          </div>
                        </div>
                        {request.contactPhone && (
                          <div className="flex items-center space-x-2 text-sm text-slate-600 bg-white px-3 py-1 rounded-lg border border-blue-200">
                            <FaPhone className="h-3 w-3 text-blue-600" />
                            <span className="font-medium">{request.contactPhone}</span>
                          </div>
                        )}
                      </div>

                      {/* Medical History Summary */}
                      {request.medicalHistory && (
                        <div className="bg-white border border-blue-200 rounded-lg p-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <FaHeart className="h-3 w-3 text-red-500" />
                            <span className="text-xs font-medium text-slate-700">Medical Summary</span>
                          </div>
                          <p className="text-xs text-slate-600 leading-relaxed">{request.medicalHistory}</p>
                        </div>
                      )}
                    </div>

                    {/* Enhanced Request Details Grid */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                      {/* Location & Distance */}
                      <div className="bg-slate-50 border border-slate-200 rounded-lg p-3">
                        <div className="flex items-center space-x-2 mb-2">
                          <FaMapPin className="h-4 w-4 text-emerald-600" />
                          <span className="text-xs font-medium text-slate-700">Location & Distance</span>
                        </div>
                        <p className="text-sm text-slate-900 font-medium mb-1">{request.address}</p>
                        <div className="flex items-center space-x-3 text-xs text-slate-600">
                          <span className="flex items-center space-x-1">
                            <FaRoute className="h-3 w-3" />
                            <span>{request.distance || 0} km away</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <FaClock className="h-3 w-3" />
                            <span>{request.travelTime || 0} min travel</span>
                          </span>
                        </div>
                      </div>

                      {/* Schedule & Duration */}
                      <div className="bg-slate-50 border border-slate-200 rounded-lg p-3">
                        <div className="flex items-center space-x-2 mb-2">
                          <FaCalendarAlt className="h-4 w-4 text-blue-600" />
                          <span className="text-xs font-medium text-slate-700">Schedule & Duration</span>
                        </div>
                        <p className="text-sm text-slate-900 font-medium mb-1">{formatDate(request.scheduledDate)}</p>
                        <div className="flex items-center space-x-3 text-xs text-slate-600">
                          <span className="flex items-center space-x-1">
                            <FaClockSolid className="h-3 w-3" />
                            <span>{request.estimatedDuration}h duration</span>
                          </span>
                        </div>
                      </div>

                      {/* Budget & Rate */}
                      <div className="bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200 rounded-lg p-3">
                        <div className="flex items-center space-x-2 mb-2">
                          <FaDollarSign className="h-4 w-4 text-emerald-600" />
                          <span className="text-xs font-medium text-slate-700">Budget</span>
                        </div>
                        <p className="text-lg font-bold text-emerald-700">EGP {request.budget.toLocaleString()}</p>
                        <p className="text-xs text-emerald-600">
                          ~EGP {Math.round(request.budget / request.estimatedDuration)}/hour
                        </p>
                      </div>

                      {/* Required Certifications */}
                      {request.requiredCertifications && request.requiredCertifications.length > 0 && (
                        <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <FaCertificate className="h-4 w-4 text-amber-600" />
                            <span className="text-xs font-medium text-slate-700">Required Certifications</span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {request.requiredCertifications.map((cert, index) => (
                              <span key={index} className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-amber-100 text-amber-800">
                                {cert}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Special Requirements */}
                    {request.specialRequirements && (
                      <div className="bg-amber-50 border border-amber-200 rounded-xl p-4 mb-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <FaExclamation className="h-4 w-4 text-amber-600" />
                          <h5 className="text-sm font-semibold text-amber-800">Special Requirements</h5>
                        </div>
                        <p className="text-sm text-amber-700 leading-relaxed">{request.specialRequirements}</p>
                      </div>
                    )}

                    {/* Additional Notes */}
                    {request.notes && (
                      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <FaInfoCircle className="h-4 w-4 text-blue-600" />
                          <h5 className="text-sm font-semibold text-blue-800">Additional Notes</h5>
                        </div>
                        <p className="text-sm text-blue-700 leading-relaxed">{request.notes}</p>
                      </div>
                    )}

                    {/* Request Metadata */}
                    <div className="flex items-center justify-between pt-4 border-t border-slate-200 text-xs text-slate-500">
                      <div className="flex items-center space-x-4">
                        <span className="flex items-center space-x-1">
                          <FaCalendarAlt className="h-3 w-3" />
                          <span>Posted {formatDateOnly(request.createdAt)}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <FaEye className="h-3 w-3" />
                          <span>ID: {request.id.slice(-8)}</span>
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <FaEllipsisV className="h-3 w-3 text-slate-400" />
                      </div>
                    </div>
                  </div>

                  {/* Premium Action Buttons */}
                  <div className="px-6 py-4 bg-gradient-to-r from-slate-50 to-blue-50 border-t border-slate-200">
                    <div className="flex items-center justify-between">
                      {/* Primary Actions */}
                      <div className="flex items-center space-x-3">
                        {!request.status || request.status === 'pending' ? (
                          <>
                            <button
                              onClick={() => openApplicationModal(request.id)}
                              disabled={applyingToRequest === request.id}
                              className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 shadow-lg"
                            >
                              {applyingToRequest === request.id ? (
                                <>
                                  <FaSpinner className="animate-spin h-4 w-4 mr-2" />
                                  Applying...
                                </>
                              ) : (
                                <>
                                  <FaEdit className="h-4 w-4 mr-2" />
                                  Apply Now
                                </>
                              )}
                            </button>
                            <button
                              onClick={() => handleApplyToRequest(request.id)}
                              disabled={applyingToRequest === request.id}
                              className="inline-flex items-center px-4 py-3 border border-slate-300 text-sm font-medium rounded-xl text-slate-700 bg-white hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm"
                            >
                              <FaArrowRight className="h-4 w-4 mr-2" />
                              Quick Apply
                            </button>
                          </>
                        ) : request.status === 'in_progress' ? (
                          <button
                            onClick={() => openStatusChangeModal(request.id, 'work_in_progress')}
                            className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 transition-all duration-200 transform hover:scale-105 shadow-lg"
                          >
                            <FaPlay className="h-4 w-4 mr-2" />
                            Start Work
                          </button>
                        ) : request.status === 'work_in_progress' ? (
                          <div className="flex items-center space-x-3">
                            <button
                              onClick={() => openStatusChangeModal(request.id, 'completed')}
                              className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 transition-all duration-200 transform hover:scale-105 shadow-lg"
                            >
                              <FaCheck className="h-4 w-4 mr-2" />
                              Complete Work
                            </button>
                            <button
                              onClick={() => openStatusChangeModal(request.id, 'cancelled')}
                              className="inline-flex items-center px-4 py-3 border border-red-300 text-sm font-medium rounded-xl text-red-700 bg-white hover:bg-red-50 transition-all duration-200 shadow-sm"
                            >
                              <FaStop className="h-4 w-4 mr-2" />
                              Cancel Work
                            </button>
                          </div>
                        ) : (
                          <div className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium ${getStatusColor(request.status)}`}>
                            <span className="capitalize">{request.status.replace('_', ' ')}</span>
                          </div>
                        )}
                      </div>

                      {/* Secondary Actions */}
                      <div className="flex items-center space-x-2">
                        <button className="p-2 text-slate-400 hover:text-slate-600 hover:bg-white rounded-lg transition-colors duration-200" title="Save to favorites">
                          <FaHeart className="h-4 w-4" />
                        </button>
                        <button className="p-2 text-slate-400 hover:text-slate-600 hover:bg-white rounded-lg transition-colors duration-200" title="View details">
                          <FaEye className="h-4 w-4" />
                        </button>
                        {request.status && request.status !== 'pending' && (
                          <button className="p-2 text-slate-400 hover:text-slate-600 hover:bg-white rounded-lg transition-colors duration-200" title="View history">
                            <FaHistory className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          </div>
        )}

        {/* My Applications Tab */}
        {activeTab === 'applications' && (
          <div className="bg-white shadow-xl rounded-2xl border border-slate-200 overflow-hidden">
            <div className="px-6 py-6 border-b border-slate-200 bg-gradient-to-r from-slate-50 to-blue-50">
              <h3 className="text-xl font-bold text-slate-900 mb-1">My Applications</h3>
              <p className="text-sm text-slate-600">Track the status of your job applications</p>
            </div>

            {myApplications.length === 0 ? (
              <div className="px-6 py-16 text-center">
                <div className="mx-auto w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4">
                  <FaClipboardList className="h-8 w-8 text-slate-400" />
                </div>
                <h3 className="text-lg font-medium text-slate-900 mb-2">No applications yet</h3>
                <p className="text-sm text-slate-600 mb-6">
                  Start applying to available requests to see your applications here.
                </p>
                <button
                  onClick={() => setActiveTab('available')}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700"
                >
                  Browse Available Requests
                </button>
              </div>
            ) : (
              <div className="p-6">
                {/* Application Status Summary */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  {[
                    { status: 'pending', label: 'Pending', count: myApplications.filter(app => app.status === 'pending').length, color: 'bg-amber-100 text-amber-800' },
                    { status: 'under_review', label: 'Under Review', count: myApplications.filter(app => app.status === 'under_review').length, color: 'bg-blue-100 text-blue-800' },
                    { status: 'accepted', label: 'Accepted', count: myApplications.filter(app => app.status === 'accepted').length, color: 'bg-emerald-100 text-emerald-800' },
                    { status: 'rejected', label: 'Rejected', count: myApplications.filter(app => app.status === 'rejected').length, color: 'bg-red-100 text-red-800' }
                  ].map((item) => (
                    <div key={item.status} className="bg-white border border-slate-200 rounded-xl p-4 text-center">
                      <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full ${item.color} mb-2`}>
                        <span className="text-lg font-bold">{item.count}</span>
                      </div>
                      <p className="text-sm font-medium text-slate-900">{item.label}</p>
                    </div>
                  ))}
                </div>

                {/* Applications List */}
                <div className="space-y-4">
                  {myApplications.map((application) => (
                    <div key={application.id} className="bg-slate-50 border border-slate-200 rounded-xl p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h4 className="text-lg font-semibold text-slate-900">Request #{application.requestId.slice(-8)}</h4>
                          <p className="text-sm text-slate-600">Applied {formatDate(application.appliedAt)}</p>
                        </div>
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(application.status)}`}>
                          {application.status.replace('_', ' ')}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-slate-700">Proposed Rate:</span>
                          <span className="ml-2 text-slate-900">EGP {application.proposedRate}/hour</span>
                        </div>
                        <div>
                          <span className="font-medium text-slate-700">Start Time:</span>
                          <span className="ml-2 text-slate-900">{formatDate(application.estimatedStartTime)}</span>
                        </div>
                      </div>

                      {application.coverLetter && (
                        <div className="mt-4 p-3 bg-white border border-slate-200 rounded-lg">
                          <h5 className="text-sm font-medium text-slate-700 mb-2">Cover Letter</h5>
                          <p className="text-sm text-slate-600">{application.coverLetter}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Active Work Tab */}
        {activeTab === 'active' && (
          <div className="bg-white shadow-xl rounded-2xl border border-slate-200 overflow-hidden">
            <div className="px-6 py-6 border-b border-slate-200 bg-gradient-to-r from-slate-50 to-emerald-50">
              <h3 className="text-xl font-bold text-slate-900 mb-1">Active Work</h3>
              <p className="text-sm text-slate-600">Manage your ongoing and assigned requests</p>
            </div>

            <div className="px-6 py-16 text-center">
              <div className="mx-auto w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4">
                <FaPlay className="h-8 w-8 text-slate-400" />
              </div>
              <h3 className="text-lg font-medium text-slate-900 mb-2">No active work</h3>
              <p className="text-sm text-slate-600 mb-6">
                Once you're assigned to requests, they'll appear here for you to manage.
              </p>
            </div>
          </div>
        )}

        {/* Enhanced Multi-Step Application Modal */}
        {showApplicationModal && currentRequestId && (
          <div className="fixed inset-0 bg-slate-900 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
            <div className="relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl border border-slate-200 overflow-hidden">
              {/* Modal Header */}
              <div className="px-6 py-4 border-b border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-xl font-bold text-slate-900">Apply to Request</h3>
                    <p className="text-sm text-slate-600 mt-1">
                      Step {applicationStep} of 3 - Complete your application
                    </p>
                  </div>
                  <button
                    onClick={() => {
                      setShowApplicationModal(false);
                      setCurrentRequestId(null);
                      setApplicationStep(1);
                      resetApplicationForm();
                    }}
                    className="p-2 text-slate-400 hover:text-slate-600 hover:bg-white rounded-lg transition-colors duration-200"
                  >
                    <FaTimes className="h-5 w-5" />
                  </button>
                </div>

                {/* Progress Steps */}
                <div className="flex items-center space-x-4 mt-4">
                  {[
                    { step: 1, title: 'Personal Info', icon: FaUser },
                    { step: 2, title: 'Application Details', icon: FaEdit },
                    { step: 3, title: 'Review & Submit', icon: FaCheck }
                  ].map((item) => (
                    <div key={item.step} className="flex items-center">
                      <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-200 ${
                        applicationStep >= item.step
                          ? 'bg-blue-600 border-blue-600 text-white'
                          : 'bg-white border-slate-300 text-slate-400'
                      }`}>
                        {applicationStep > item.step ? (
                          <FaCheck className="h-4 w-4" />
                        ) : (
                          <item.icon className="h-4 w-4" />
                        )}
                      </div>
                      <span className={`ml-2 text-sm font-medium ${
                        applicationStep >= item.step ? 'text-blue-600' : 'text-slate-400'
                      }`}>
                        {item.title}
                      </span>
                      {item.step < 3 && (
                        <div className={`w-8 h-0.5 mx-4 ${
                          applicationStep > item.step ? 'bg-blue-600' : 'bg-slate-300'
                        }`} />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Modal Content */}
              <div className="px-6 py-6">
                {/* Step 1: Personal Information */}
                {applicationStep === 1 && (
                  <div className="space-y-6">
                    <div className="text-center mb-6">
                      <h4 className="text-lg font-semibold text-slate-900 mb-2">Personal Information</h4>
                      <p className="text-sm text-slate-600">Confirm your details for this application</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Full Name
                        </label>
                        <input
                          type="text"
                          value={user?.name || ''}
                          disabled
                          className="w-full px-4 py-3 border border-slate-300 rounded-lg bg-slate-50 text-slate-600"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Email Address
                        </label>
                        <input
                          type="email"
                          value={user?.email || ''}
                          disabled
                          className="w-full px-4 py-3 border border-slate-300 rounded-lg bg-slate-50 text-slate-600"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Phone Number
                        </label>
                        <input
                          type="tel"
                          placeholder="Enter your phone number"
                          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Years of Experience
                        </label>
                        <select className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                          <option value="">Select experience</option>
                          <option value="1-2">1-2 years</option>
                          <option value="3-5">3-5 years</option>
                          <option value="6-10">6-10 years</option>
                          <option value="10+">10+ years</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Relevant Certifications
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {['CPR Certified', 'First Aid', 'Pediatric Care', 'Elderly Care', 'Mental Health', 'Post-Op Care'].map((cert) => (
                          <label key={cert} className="flex items-center p-3 border border-slate-200 rounded-lg hover:bg-slate-50 cursor-pointer">
                            <input type="checkbox" className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded mr-3" />
                            <span className="text-sm text-slate-700">{cert}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 2: Application Details */}
                {applicationStep === 2 && (
                  <div className="space-y-6">
                    <div className="text-center mb-6">
                      <h4 className="text-lg font-semibold text-slate-900 mb-2">Application Details</h4>
                      <p className="text-sm text-slate-600">Provide details about your application</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Proposed Hourly Rate (EGP)
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            value={applicationForm.proposedRate}
                            onChange={(e) => setApplicationForm({...applicationForm, proposedRate: parseInt(e.target.value) || 0})}
                            min="50"
                            max="1000"
                            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="150"
                          />
                          <span className="absolute right-3 top-3 text-slate-500 text-sm">EGP/hour</span>
                        </div>
                        <p className="text-xs text-slate-500 mt-1">Recommended: EGP 120-200/hour</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Estimated Start Time
                        </label>
                        <input
                          type="datetime-local"
                          value={applicationForm.estimatedStartTime}
                          onChange={(e) => setApplicationForm({...applicationForm, estimatedStartTime: e.target.value})}
                          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Cover Letter
                      </label>
                      <textarea
                        value={applicationForm.coverLetter}
                        onChange={(e) => setApplicationForm({...applicationForm, coverLetter: e.target.value})}
                        rows={5}
                        className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Tell the patient why you're the right nurse for this job. Highlight your relevant experience and qualifications..."
                      />
                      <p className="text-xs text-slate-500 mt-1">{applicationForm.coverLetter.length}/500 characters</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Availability Notes
                      </label>
                      <textarea
                        value={applicationForm.availabilityNotes}
                        onChange={(e) => setApplicationForm({...applicationForm, availabilityNotes: e.target.value})}
                        rows={3}
                        className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Any scheduling preferences, availability constraints, or special notes..."
                      />
                    </div>
                  </div>
                )}

                {/* Step 3: Review & Submit */}
                {applicationStep === 3 && (
                  <div className="space-y-6">
                    <div className="text-center mb-6">
                      <h4 className="text-lg font-semibold text-slate-900 mb-2">Review Your Application</h4>
                      <p className="text-sm text-slate-600">Please review your application before submitting</p>
                    </div>

                    {/* Application Summary */}
                    <div className="bg-slate-50 border border-slate-200 rounded-xl p-6">
                      <h5 className="text-sm font-semibold text-slate-900 mb-4">Application Summary</h5>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <span className="text-sm font-medium text-slate-700">Proposed Rate:</span>
                          <span className="ml-2 text-sm text-slate-900">EGP {applicationForm.proposedRate}/hour</span>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-slate-700">Start Time:</span>
                          <span className="ml-2 text-sm text-slate-900">
                            {applicationForm.estimatedStartTime ? formatDate(applicationForm.estimatedStartTime) : 'Not specified'}
                          </span>
                        </div>
                      </div>

                      {applicationForm.coverLetter && (
                        <div className="mb-4">
                          <span className="text-sm font-medium text-slate-700 block mb-2">Cover Letter:</span>
                          <div className="bg-white border border-slate-200 rounded-lg p-3">
                            <p className="text-sm text-slate-600">{applicationForm.coverLetter}</p>
                          </div>
                        </div>
                      )}

                      {applicationForm.availabilityNotes && (
                        <div>
                          <span className="text-sm font-medium text-slate-700 block mb-2">Availability Notes:</span>
                          <div className="bg-white border border-slate-200 rounded-lg p-3">
                            <p className="text-sm text-slate-600">{applicationForm.availabilityNotes}</p>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Terms and Conditions */}
                    <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                      <div className="flex items-start space-x-3">
                        <input type="checkbox" className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded mt-0.5" />
                        <div className="text-sm">
                          <p className="text-blue-900 font-medium mb-1">Terms and Conditions</p>
                          <p className="text-blue-700">
                            I agree to the platform's terms of service and confirm that all information provided is accurate.
                            I understand that false information may result in application rejection.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Modal Footer */}
              <div className="px-6 py-4 border-t border-slate-200 bg-slate-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {applicationStep > 1 && (
                      <button
                        onClick={() => setApplicationStep(applicationStep - 1)}
                        className="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-all duration-200"
                      >
                        <FaArrowRight className="h-4 w-4 mr-2 rotate-180" />
                        Previous
                      </button>
                    )}
                  </div>

                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => {
                        setShowApplicationModal(false);
                        setCurrentRequestId(null);
                        setApplicationStep(1);
                        resetApplicationForm();
                      }}
                      className="px-4 py-2 border border-slate-300 rounded-lg text-sm font-medium text-slate-700 bg-white hover:bg-slate-50 transition-all duration-200"
                    >
                      Cancel
                    </button>

                    {applicationStep < 3 ? (
                      <button
                        onClick={() => setApplicationStep(applicationStep + 1)}
                        className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-semibold rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg"
                      >
                        Next
                        <FaArrowRight className="h-4 w-4 ml-2" />
                      </button>
                    ) : (
                      <button
                        onClick={() => handleApplyToRequest(currentRequestId, applicationForm)}
                        disabled={applyingToRequest === currentRequestId}
                        className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-semibold rounded-lg text-white bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
                      >
                        {applyingToRequest === currentRequestId ? (
                          <>
                            <FaSpinner className="animate-spin h-4 w-4 mr-2" />
                            Submitting...
                          </>
                        ) : (
                          <>
                            <FaCheck className="h-4 w-4 mr-2" />
                            Submit Application
                          </>
                        )}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
              </div>
            </div>
          </div>
        )}

        {/* Bulk Application Modal */}
        {showBulkApplicationModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Apply to {selectedRequests.size} Selected Requests
                  </h3>
                  <button
                    onClick={() => {
                      setShowBulkApplicationModal(false);
                      resetBulkApplicationForm();
                      setBulkApplicationResults({});
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FaTimes className="h-6 w-6" />
                  </button>
                </div>

                {Object.keys(bulkApplicationResults).length > 0 ? (
                  <div className="space-y-3 mb-6">
                    <h4 className="text-sm font-medium text-gray-900">Application Results:</h4>
                    {Object.entries(bulkApplicationResults).map(([requestId, result]) => {
                      const request = availableRequests.find(r => r.id === requestId);
                      return (
                        <div key={requestId} className={`p-3 rounded-lg border ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                          <div className="flex items-center space-x-2">
                            {result.success ? (
                              <FaCheck className="h-4 w-4 text-green-600" />
                            ) : (
                              <FaTimes className="h-4 w-4 text-red-600" />
                            )}
                            <span className="text-sm font-medium">
                              {request?.title || `Request ${requestId.slice(-8)}`}
                            </span>
                          </div>
                          {!result.success && result.error && (
                            <p className="text-sm text-red-600 mt-1">{result.error}</p>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Cover Letter */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Cover Letter (will be used for all applications)
                      </label>
                      <textarea
                        value={bulkApplicationForm.coverLetter}
                        onChange={(e) => setBulkApplicationForm({...bulkApplicationForm, coverLetter: e.target.value})}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Tell patients why you're the right nurse for these jobs..."
                      />
                    </div>

                    {/* Proposed Rate */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Proposed Hourly Rate (EGP)
                      </label>
                      <input
                        type="number"
                        value={bulkApplicationForm.proposedRate}
                        onChange={(e) => setBulkApplicationForm({...bulkApplicationForm, proposedRate: parseInt(e.target.value) || 0})}
                        min="50"
                        max="1000"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    {/* Estimated Start Time */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Estimated Start Time
                      </label>
                      <input
                        type="datetime-local"
                        value={bulkApplicationForm.estimatedStartTime}
                        onChange={(e) => setBulkApplicationForm({...bulkApplicationForm, estimatedStartTime: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    {/* Availability Notes */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Availability Notes
                      </label>
                      <textarea
                        value={bulkApplicationForm.availabilityNotes}
                        onChange={(e) => setBulkApplicationForm({...bulkApplicationForm, availabilityNotes: e.target.value})}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Any scheduling preferences or availability constraints..."
                      />
                    </div>

                    {/* Selected Requests Preview */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Selected Requests:</h4>
                      <div className="space-y-2">
                        {Array.from(selectedRequests).map(requestId => {
                          const request = availableRequests.find(r => r.id === requestId);
                          return (
                            <div key={requestId} className="text-sm text-gray-600">
                              • {request?.title} - EGP {request?.budget}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => {
                      setShowBulkApplicationModal(false);
                      resetBulkApplicationForm();
                      setBulkApplicationResults({});
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    {Object.keys(bulkApplicationResults).length > 0 ? 'Close' : 'Cancel'}
                  </button>
                  {Object.keys(bulkApplicationResults).length === 0 && (
                    <button
                      onClick={handleBulkApplication}
                      disabled={isBulkApplying}
                      className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isBulkApplying ? (
                        <>
                          <FaSpinner className="animate-spin h-4 w-4 mr-2 inline" />
                          Submitting Applications...
                        </>
                      ) : (
                        `Submit ${selectedRequests.size} Applications`
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Status Change Modal */}
        {showStatusModal && statusChangeRequest && (
          <div className="fixed inset-0 bg-slate-900 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
            <div className="relative w-full max-w-md bg-white rounded-2xl shadow-2xl border border-slate-200 overflow-hidden">
              {/* Modal Header */}
              <div className="px-6 py-4 border-b border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-bold text-slate-900">Update Request Status</h3>
                    <p className="text-sm text-slate-600 mt-1">
                      Change status to: <span className="font-medium capitalize">{statusChangeRequest.newStatus.replace('_', ' ')}</span>
                    </p>
                  </div>
                  <button
                    onClick={() => {
                      setShowStatusModal(false);
                      setStatusChangeRequest(null);
                      setStatusChangeReason('');
                    }}
                    className="p-2 text-slate-400 hover:text-slate-600 hover:bg-white rounded-lg transition-colors duration-200"
                  >
                    <FaTimes className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="px-6 py-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Reason for status change
                    </label>
                    <textarea
                      value={statusChangeReason}
                      onChange={(e) => setStatusChangeReason(e.target.value)}
                      rows={4}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Please provide a reason for this status change..."
                    />
                  </div>

                  {statusChangeRequest.newStatus === 'completed' && (
                    <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <FaCheckCircle className="h-4 w-4 text-emerald-600" />
                        <span className="text-sm font-medium text-emerald-800">Completing Request</span>
                      </div>
                      <p className="text-sm text-emerald-700">
                        This will mark the request as completed and notify the patient.
                      </p>
                    </div>
                  )}

                  {statusChangeRequest.newStatus === 'cancelled' && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <FaTimesCircle className="h-4 w-4 text-red-600" />
                        <span className="text-sm font-medium text-red-800">Cancelling Request</span>
                      </div>
                      <p className="text-sm text-red-700">
                        This will cancel the request and may affect your rating. Please provide a valid reason.
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Modal Footer */}
              <div className="px-6 py-4 border-t border-slate-200 bg-slate-50">
                <div className="flex items-center justify-end space-x-3">
                  <button
                    onClick={() => {
                      setShowStatusModal(false);
                      setStatusChangeRequest(null);
                      setStatusChangeReason('');
                    }}
                    className="px-4 py-2 border border-slate-300 rounded-lg text-sm font-medium text-slate-700 bg-white hover:bg-slate-50 transition-all duration-200"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => handleStatusChange(statusChangeRequest.requestId, statusChangeRequest.newStatus, statusChangeReason)}
                    disabled={!statusChangeReason.trim()}
                    className="px-6 py-2 border border-transparent text-sm font-semibold rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
                  >
                    Update Status
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
