import { useEffect, useState, useCallback, useMemo, memo } from 'react';
import { useAuth } from '../../lib/auth';
import { apiService } from '../../lib/api';
import {
  FaSearch,
  FaFilter,
  FaSort,
  FaEye,
  FaEdit,
  FaCheck,
  FaTimes,
  FaClock,
  FaUser,
  FaUserNurse,
  FaClipboardList,
  FaChartBar,
  FaSync,
  FaDownload,
  FaChevronDown,
  FaChevronUp,
  FaExclamationTriangle,
  FaCheckCircle,
  FaTimesCircle,
  FaSpinner,
  FaBell,
  FaCalendarAlt,
  FaMapMarkerAlt,
  FaDollarSign,
  FaStethoscope,
  FaHeart,
  FaStar,
  FaEllipsisV,
  FaArrowRight,
  FaHistory,
  FaPhone,
  FaEnvelope
} from 'react-icons/fa';
import '../../styles/nurse-dashboard.css';

// Enhanced Application Interface
interface Application {
  id: string;
  title: string;
  description: string;
  status: string;
  createdAt: string;
  acceptedAt?: string;
  completedAt?: string;
  updatedAt?: string;
  budget?: number;
  estimatedDuration?: number;
  urgencyLevel?: 'low' | 'medium' | 'high';
  serviceType?: string;
  location?: string;
  specialRequirements?: string;
  patient: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    address?: string;
  } | null;
  nurse: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    rating?: number;
    experience?: string;
    certifications?: string[];
  } | null;
  applicationStatus: 'Pending' | 'Under Review' | 'Accepted' | 'Completed' | 'Rejected' | 'Withdrawn';
  appliedDate: string;
  proposedRate?: number;
  coverLetter?: string;
  estimatedStartTime?: string;
  availabilityNotes?: string;
  statusHistory?: {
    status: string;
    timestamp: string;
    notes?: string;
    updatedBy?: string;
  }[];
}

// Filter and Sort Options
interface FilterOptions {
  status: string[];
  serviceTypes: string[];
  urgencyLevels: string[];
  dateRange: [string, string];
  budgetRange: [number, number];
}

interface SortOption {
  field: 'date' | 'status' | 'budget' | 'urgency' | 'nurse' | 'patient';
  direction: 'asc' | 'desc';
}

// Statistics Interface
interface ApplicationStats {
  total: number;
  pending: number;
  underReview: number;
  accepted: number;
  completed: number;
  rejected: number;
  withdrawn: number;
  averageResponseTime: number;
  completionRate: number;
}

export default function AdminApplications() {
  const { user, loading } = useAuth();

  // Core state
  const [applications, setApplications] = useState<Application[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<Application[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [error, setError] = useState<string>('');
  const [stats, setStats] = useState<ApplicationStats | null>(null);

  // UI state
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedApplications, setSelectedApplications] = useState<Set<string>>(new Set());
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('cards');
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);

  // Filter and sort state
  const [filters, setFilters] = useState<FilterOptions>({
    status: [],
    serviceTypes: [],
    urgencyLevels: [],
    dateRange: ['', ''],
    budgetRange: [0, 1000]
  });
  const [sortOption, setSortOption] = useState<SortOption>({
    field: 'date',
    direction: 'desc'
  });

  // Enhanced data loading with statistics
  const loadApplications = useCallback(async () => {
    try {
      setError('');
      setLoadingData(true);
      console.log('Loading applications for admin...');

      const applicationsData = await apiService.getApplications();
      console.log('Applications data received:', applicationsData);

      // Handle the response data structure
      let applications = [];
      if (Array.isArray(applicationsData)) {
        applications = applicationsData;
      } else if (applicationsData && Array.isArray(applicationsData.data)) {
        applications = applicationsData.data;
      } else if (applicationsData && applicationsData.success && Array.isArray(applicationsData.data)) {
        applications = applicationsData.data;
      }

      // Enhance applications with mock data for demo
      const enhancedApplications = applications.map((app: Application) => ({
        ...app,
        budget: app.budget || Math.floor(Math.random() * 500) + 100,
        estimatedDuration: app.estimatedDuration || Math.floor(Math.random() * 8) + 2,
        urgencyLevel: app.urgencyLevel || ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
        serviceType: app.serviceType || ['elderly_care', 'post_surgery', 'pediatric_care', 'chronic_care'][Math.floor(Math.random() * 4)],
        proposedRate: Math.floor(Math.random() * 100) + 100,
        coverLetter: 'I am interested in this position and believe I can provide excellent care.',
        statusHistory: [
          {
            status: app.applicationStatus,
            timestamp: app.createdAt,
            notes: 'Application submitted'
          }
        ]
      }));

      console.log('Enhanced applications:', enhancedApplications);
      setApplications(enhancedApplications);

      // Calculate statistics
      const stats: ApplicationStats = {
        total: enhancedApplications.length,
        pending: enhancedApplications.filter((app: { applicationStatus: string; }) => app.applicationStatus === 'Pending').length,
        underReview: enhancedApplications.filter((app: { applicationStatus: string; }) => app.applicationStatus === 'Under Review').length,
        accepted: enhancedApplications.filter((app: { applicationStatus: string; }) => app.applicationStatus === 'Accepted').length,
        completed: enhancedApplications.filter((app: { applicationStatus: string; }) => app.applicationStatus === 'Completed').length,
        rejected: enhancedApplications.filter((app: { applicationStatus: string; }) => app.applicationStatus === 'Rejected').length,
        withdrawn: enhancedApplications.filter((app: { applicationStatus: string; }) => app.applicationStatus === 'Withdrawn').length,
        averageResponseTime: 2.5, // Mock data
        completionRate: 85 // Mock data
      };

      setStats(stats);
    } catch (err: any) {
      console.error('Error loading applications:', err);
      setError(`Failed to load applications: ${err.message}`);
    } finally {
      setLoadingData(false);
    }
  }, []);

  // Advanced filtering and sorting
  const applyFiltersAndSort = useCallback(() => {
    let filtered = [...applications];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(app =>
        app.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.patient?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.nurse?.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(app =>
        filters.status.includes(app.applicationStatus)
      );
    }

    // Apply service type filter
    if (filters.serviceTypes.length > 0) {
      filtered = filtered.filter(app =>
        app.serviceType && filters.serviceTypes.includes(app.serviceType)
      );
    }

    // Apply urgency filter
    if (filters.urgencyLevels.length > 0) {
      filtered = filtered.filter(app =>
        app.urgencyLevel && filters.urgencyLevels.includes(app.urgencyLevel)
      );
    }

    // Apply budget range filter
    filtered = filtered.filter(app =>
      (app.budget || 0) >= filters.budgetRange[0] && (app.budget || 0) <= filters.budgetRange[1]
    );

    // Apply date range filter
    if (filters.dateRange[0] && filters.dateRange[1]) {
      const startDate = new Date(filters.dateRange[0]);
      const endDate = new Date(filters.dateRange[1]);
      filtered = filtered.filter(app => {
        const appDate = new Date(app.createdAt);
        return appDate >= startDate && appDate <= endDate;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortOption.field) {
        case 'date':
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
        case 'status':
          aValue = a.applicationStatus;
          bValue = b.applicationStatus;
          break;
        case 'budget':
          aValue = a.budget || 0;
          bValue = b.budget || 0;
          break;
        case 'urgency':
          const urgencyOrder = { high: 3, medium: 2, low: 1 };
          aValue = urgencyOrder[a.urgencyLevel as keyof typeof urgencyOrder] || 0;
          bValue = urgencyOrder[b.urgencyLevel as keyof typeof urgencyOrder] || 0;
          break;
        case 'nurse':
          aValue = a.nurse?.name || '';
          bValue = b.nurse?.name || '';
          break;
        case 'patient':
          aValue = a.patient?.name || '';
          bValue = b.patient?.name || '';
          break;
        default:
          aValue = 0;
          bValue = 0;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOption.direction === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (sortOption.direction === 'asc') {
        return (aValue as number) - (bValue as number);
      } else {
        return (bValue as number) - (aValue as number);
      }
    });

    setFilteredApplications(filtered);
  }, [applications, searchQuery, filters, sortOption]);

  // Apply filters whenever dependencies change
  useEffect(() => {
    applyFiltersAndSort();
  }, [applyFiltersAndSort]);

  // Utility functions
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDateRelative = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} week${Math.ceil(diffDays / 7) > 1 ? 's' : ''} ago`;
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} month${Math.ceil(diffDays / 30) > 1 ? 's' : ''} ago`;
    return `${Math.ceil(diffDays / 365)} year${Math.ceil(diffDays / 365) > 1 ? 's' : ''} ago`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Pending':
        return 'text-amber-600 bg-amber-50 border-amber-200';
      case 'Under Review':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'Accepted':
        return 'text-emerald-600 bg-emerald-50 border-emerald-200';
      case 'Completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'Rejected':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'Withdrawn':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency.toLowerCase()) {
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'medium':
        return 'text-amber-600 bg-amber-50 border-amber-200';
      case 'low':
        return 'text-emerald-600 bg-emerald-50 border-emerald-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getServiceTypeColor = (serviceType: string) => {
    const colors = {
      'elderly_care': 'bg-purple-50 text-purple-700 border-purple-200',
      'post_surgery': 'bg-red-50 text-red-700 border-red-200',
      'pediatric_care': 'bg-pink-50 text-pink-700 border-pink-200',
      'chronic_care': 'bg-orange-50 text-orange-700 border-orange-200',
      'mental_health': 'bg-indigo-50 text-indigo-700 border-indigo-200',
      'rehabilitation': 'bg-green-50 text-green-700 border-green-200',
    };
    return colors[serviceType as keyof typeof colors] || 'bg-gray-50 text-gray-700 border-gray-200';
  };

  // Application management functions
  const handleStatusChange = async (applicationId: string, newStatus: string) => {
    try {
      console.log('Changing application status:', applicationId, 'to:', newStatus);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update local state
      setApplications(prev => prev.map(app =>
        app.id === applicationId
          ? {
              ...app,
              applicationStatus: newStatus as any,
              updatedAt: new Date().toISOString(),
              statusHistory: [
                ...(app.statusHistory || []),
                {
                  status: newStatus,
                  timestamp: new Date().toISOString(),
                  notes: `Status changed to ${newStatus}`,
                  updatedBy: user?.name || 'Admin'
                }
              ]
            }
          : app
      ));

    } catch (error) {
      setError('Failed to update application status');
    }
  };

  const toggleApplicationSelection = (applicationId: string) => {
    const newSelected = new Set(selectedApplications);
    if (newSelected.has(applicationId)) {
      newSelected.delete(applicationId);
    } else {
      newSelected.add(applicationId);
    }
    setSelectedApplications(newSelected);
  };

  const openApplicationDetails = (application: Application) => {
    setSelectedApplication(application);
    setShowApplicationModal(true);
  };

  useEffect(() => {
    if (user?.role === 'admin') {
      loadApplications();
    }
  }, [user]);

  // Premium Loading State
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-lg mb-4">
              <div className="animate-spin h-8 w-8 border-4 border-blue-600 border-t-transparent rounded-full"></div>
            </div>
            <h3 className="text-lg font-medium text-slate-900 mb-2">Loading Applications</h3>
            <p className="text-sm text-slate-600">Fetching the latest application data...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
            <div className="h-8 w-8 text-red-600">⚠️</div>
          </div>
          <h3 className="text-lg font-medium text-slate-900 mb-2">Access Denied</h3>
          <p className="text-sm text-slate-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex">
      {/* Enhanced Sidebar */}
      <div className="w-64 bg-white shadow-xl border-r border-slate-200">
        <div className="p-6">
          {/* User Profile Section */}
          <div className="flex items-center space-x-3 mb-8">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl shadow-lg flex items-center justify-center text-white font-semibold text-lg">
              {user.name?.charAt(0) || 'A'}
            </div>
            <div>
              <h2 className="font-bold text-slate-900">CareConnect</h2>
              <p className="text-sm text-slate-600 font-medium">Admin Portal</p>
            </div>
          </div>

          {/* Enhanced Navigation Items */}
          <nav className="space-y-2">
            <a
              href="/dashboard"
              className="flex items-center space-x-3 px-4 py-3 text-sm font-medium text-slate-700 hover:bg-blue-50 hover:text-blue-700 rounded-xl transition-all duration-200"
            >
              <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                <span className="text-lg">🏠</span>
              </div>
              <span>Dashboard</span>
            </a>
            <a
              href="/admin/users"
              className="flex items-center space-x-3 px-4 py-3 text-sm font-medium text-slate-700 hover:bg-blue-50 hover:text-blue-700 rounded-xl transition-all duration-200"
            >
              <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                <FaUser className="h-4 w-4 text-slate-600" />
              </div>
              <span>Users</span>
            </a>
            <a
              href="/admin/nurse-approvals"
              className="flex items-center space-x-3 px-4 py-3 text-sm font-medium text-slate-700 hover:bg-blue-50 hover:text-blue-700 rounded-xl transition-all duration-200 ml-6"
            >
              <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                <FaUserNurse className="h-4 w-4 text-slate-600" />
              </div>
              <span>Nurse Approvals</span>
            </a>
            <a
              href="/admin/requests"
              className="flex items-center space-x-3 px-4 py-3 text-sm font-medium text-slate-700 hover:bg-blue-50 hover:text-blue-700 rounded-xl transition-all duration-200"
            >
              <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                <FaClipboardList className="h-4 w-4 text-slate-600" />
              </div>
              <span>Requests</span>
            </a>
            <a
              href="/admin/applications"
              className="flex items-center space-x-3 px-4 py-3 text-sm font-medium bg-blue-100 text-blue-700 rounded-xl shadow-sm"
            >
              <div className="w-8 h-8 bg-blue-200 rounded-lg flex items-center justify-center">
                <FaClipboardList className="h-4 w-4 text-blue-700" />
              </div>
              <span>Applications</span>
            </a>
            <a
              href="/admin/statistics"
              className="flex items-center space-x-3 px-4 py-3 text-sm font-medium text-slate-700 hover:bg-blue-50 hover:text-blue-700 rounded-xl transition-all duration-200"
            >
              <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                <FaChartBar className="h-4 w-4 text-slate-600" />
              </div>
              <span>Statistics</span>
            </a>
          </nav>

          {/* Help and Docs */}
          <div className="mt-8 pt-8 border-t border-slate-200">
            <a
              href="/help"
              className="flex items-center space-x-3 px-4 py-3 text-sm font-medium text-slate-700 hover:bg-blue-50 hover:text-blue-700 rounded-xl transition-all duration-200 w-full"
            >
              <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                <span className="text-lg">❓</span>
              </div>
              <span>Help and Docs</span>
            </a>
          </div>
        </div>
      </div>

      {/* Enhanced Main Content */}
      <div className="flex-1 bg-white">
        <div className="p-8">
          <div className="max-w-7xl mx-auto">
            {/* Premium Header */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h1 className="text-3xl font-bold text-slate-900 tracking-tight">Applications Management</h1>
                  <p className="text-slate-600 mt-2 font-medium">Monitor and manage nurse applications to patient requests</p>
                </div>

                {/* Header Actions */}
                <div className="flex items-center space-x-3">
                  <button
                    onClick={loadApplications}
                    disabled={loadingData}
                    className="inline-flex items-center px-4 py-2 border border-slate-300 shadow-sm text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 disabled:opacity-50 transition-all duration-200"
                  >
                    <FaSync className={`h-4 w-4 mr-2 ${loadingData ? 'animate-spin' : ''}`} />
                    {loadingData ? 'Loading...' : 'Refresh'}
                  </button>
                  <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg transition-all duration-200">
                    <FaDownload className="h-4 w-4 mr-2" />
                    Export
                  </button>
                </div>
              </div>

              {/* Search and Filter Bar */}
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 bg-white border border-slate-200 rounded-xl p-4 shadow-sm">
                <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
                  {/* Search Bar */}
                  <div className="relative">
                    <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                    <input
                      type="text"
                      placeholder="Search applications..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm w-full sm:w-64"
                    />
                  </div>

                  {/* Filter Toggle */}
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className={`inline-flex items-center px-4 py-2 border text-sm font-medium rounded-lg transition-all duration-200 ${
                      showFilters
                        ? 'border-blue-500 text-blue-600 bg-blue-50'
                        : 'border-slate-300 text-slate-700 bg-white hover:bg-slate-50'
                    }`}
                  >
                    <FaFilter className="h-4 w-4 mr-2" />
                    Filters
                    {showFilters ? <FaChevronUp className="h-3 w-3 ml-2" /> : <FaChevronDown className="h-3 w-3 ml-2" />}
                  </button>

                  {/* Sort Dropdown */}
                  <select
                    value={`${sortOption.field}-${sortOption.direction}`}
                    onChange={(e) => {
                      const [field, direction] = e.target.value.split('-');
                      setSortOption({ field: field as any, direction: direction as any });
                    }}
                    className="px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
                  >
                    <option value="date-desc">Latest First</option>
                    <option value="date-asc">Oldest First</option>
                    <option value="status-asc">Status A-Z</option>
                    <option value="budget-desc">Highest Budget</option>
                    <option value="urgency-desc">Most Urgent</option>
                    <option value="nurse-asc">Nurse A-Z</option>
                    <option value="patient-asc">Patient A-Z</option>
                  </select>
                </div>

                {/* View Mode and Stats */}
                <div className="flex items-center space-x-4">
                  <div className="flex items-center border border-slate-300 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('cards')}
                      className={`px-3 py-2 text-sm font-medium transition-colors ${
                        viewMode === 'cards'
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-slate-600 hover:bg-slate-50'
                      }`}
                    >
                      Cards
                    </button>
                    <button
                      onClick={() => setViewMode('table')}
                      className={`px-3 py-2 text-sm font-medium transition-colors ${
                        viewMode === 'table'
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-slate-600 hover:bg-slate-50'
                      }`}
                    >
                      Table
                    </button>
                  </div>

                  {filteredApplications.length > 0 && (
                    <span className="text-sm text-slate-600 font-medium">
                      {filteredApplications.length} of {applications.length} applications
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                <p className="text-red-600">{error}</p>
                <button
                  onClick={loadApplications}
                  className="mt-2 text-red-700 hover:text-red-900 underline"
                >
                  Try again
                </button>
              </div>
            )}

            {/* Premium Statistics Cards */}
            {stats && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {/* Total Applications */}
                <div className="bg-white overflow-hidden shadow-lg rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                  <div className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600 mb-1">Total Applications</p>
                        <p className="text-2xl font-bold text-slate-900">{stats.total}</p>
                        <div className="flex items-center mt-2 text-xs text-emerald-600">
                          <span>+12% from last month</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl">
                        <FaClipboardList className="h-6 w-6 text-blue-600" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Pending Applications */}
                <div className="bg-white overflow-hidden shadow-lg rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                  <div className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600 mb-1">Pending Review</p>
                        <p className="text-2xl font-bold text-slate-900">{stats.pending}</p>
                        <div className="flex items-center mt-2 text-xs text-amber-600">
                          <FaClock className="h-3 w-3 mr-1" />
                          <span>Avg {stats.averageResponseTime}h response</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-center w-12 h-12 bg-amber-100 rounded-xl">
                        <FaClock className="h-6 w-6 text-amber-600" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Accepted Applications */}
                <div className="bg-white overflow-hidden shadow-lg rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                  <div className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600 mb-1">Accepted</p>
                        <p className="text-2xl font-bold text-slate-900">{stats.accepted}</p>
                        <div className="flex items-center mt-2 text-xs text-emerald-600">
                          <FaCheckCircle className="h-3 w-3 mr-1" />
                          <span>{Math.round((stats.accepted / stats.total) * 100)}% acceptance rate</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-center w-12 h-12 bg-emerald-100 rounded-xl">
                        <FaCheckCircle className="h-6 w-6 text-emerald-600" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Completion Rate */}
                <div className="bg-white overflow-hidden shadow-lg rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                  <div className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600 mb-1">Completion Rate</p>
                        <p className="text-2xl font-bold text-slate-900">{stats.completionRate}%</p>
                        <div className="flex items-center mt-2 text-xs text-green-600">
                          <FaStar className="h-3 w-3 mr-1" />
                          <span>{stats.completed} completed</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-xl">
                        <FaStar className="h-6 w-6 text-green-600" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Advanced Filters Panel */}
            {showFilters && (
              <div className="bg-white border border-slate-200 rounded-xl p-6 mb-6 shadow-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {/* Status Filter */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">Application Status</label>
                    <div className="space-y-2">
                      {['Pending', 'Under Review', 'Accepted', 'Completed', 'Rejected'].map((status) => (
                        <label key={status} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={filters.status.includes(status)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilters(prev => ({
                                  ...prev,
                                  status: [...prev.status, status]
                                }));
                              } else {
                                setFilters(prev => ({
                                  ...prev,
                                  status: prev.status.filter(s => s !== status)
                                }));
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                          />
                          <span className="ml-2 text-sm text-slate-600">{status}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Service Types */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">Service Types</label>
                    <div className="space-y-2">
                      {['elderly_care', 'post_surgery', 'pediatric_care', 'chronic_care'].map((type) => (
                        <label key={type} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={filters.serviceTypes.includes(type)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilters(prev => ({
                                  ...prev,
                                  serviceTypes: [...prev.serviceTypes, type]
                                }));
                              } else {
                                setFilters(prev => ({
                                  ...prev,
                                  serviceTypes: prev.serviceTypes.filter(t => t !== type)
                                }));
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                          />
                          <span className="ml-2 text-sm text-slate-600 capitalize">
                            {type.replace('_', ' ')}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Budget Range */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">Budget Range (EGP)</label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          placeholder="Min"
                          value={filters.budgetRange[0]}
                          onChange={(e) => setFilters(prev => ({
                            ...prev,
                            budgetRange: [parseInt(e.target.value) || 0, prev.budgetRange[1]]
                          }))}
                          className="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500"
                        />
                        <span className="text-slate-500">-</span>
                        <input
                          type="number"
                          placeholder="Max"
                          value={filters.budgetRange[1]}
                          onChange={(e) => setFilters(prev => ({
                            ...prev,
                            budgetRange: [prev.budgetRange[0], parseInt(e.target.value) || 1000]
                          }))}
                          className="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Date Range */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">Date Range</label>
                    <div className="space-y-2">
                      <input
                        type="date"
                        value={filters.dateRange[0]}
                        onChange={(e) => setFilters(prev => ({
                          ...prev,
                          dateRange: [e.target.value, prev.dateRange[1]]
                        }))}
                        className="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500"
                      />
                      <input
                        type="date"
                        value={filters.dateRange[1]}
                        onChange={(e) => setFilters(prev => ({
                          ...prev,
                          dateRange: [prev.dateRange[0], e.target.value]
                        }))}
                        className="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Filter Actions */}
                <div className="flex items-center justify-between mt-6 pt-4 border-t border-slate-200">
                  <button
                    onClick={() => {
                      setFilters({
                        status: [],
                        serviceTypes: [],
                        urgencyLevels: [],
                        dateRange: ['', ''],
                        budgetRange: [0, 1000]
                      });
                      setSearchQuery('');
                    }}
                    className="text-sm text-slate-600 hover:text-slate-900 font-medium"
                  >
                    Clear All Filters
                  </button>
                  <div className="text-sm text-slate-600">
                    Showing {filteredApplications.length} of {applications.length} applications
                  </div>
                </div>
              </div>
            )}



            {/* Applications Table */}
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Request
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Patient
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Nurse
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {loadingData ? (
                      // Loading skeleton
                      Array.from({ length: 5 }).map((_, index) => (
                        <tr key={index} className="animate-pulse">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="h-4 bg-gray-200 rounded w-48"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="h-4 bg-gray-200 rounded w-32"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="h-4 bg-gray-200 rounded w-32"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="h-4 bg-gray-200 rounded w-20"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="h-4 bg-gray-200 rounded w-24"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="h-4 bg-gray-200 rounded w-16"></div>
                          </td>
                        </tr>
                      ))
                    ) : applications.length === 0 ? (
                      <tr>
                        <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                          No applications found
                        </td>
                      </tr>
                    ) : (
                      applications.map((application) => (
                        <tr key={application.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{application.title}</div>
                              <div className="text-sm text-gray-500 truncate max-w-xs">{application.description}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {application.patient?.name || 'Unknown Patient'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {application.patient?.email}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {application.nurse?.name || 'No nurse assigned'}
                            </div>
                            {application.nurse?.email && (
                              <div className="text-sm text-gray-500">
                                {application.nurse.email}
                              </div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              application.applicationStatus === 'Completed' ? 'bg-green-100 text-green-800' :
                              application.applicationStatus === 'Accepted' ? 'bg-blue-100 text-blue-800' :
                              application.applicationStatus === 'Rejected' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {application.applicationStatus}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(application.createdAt)}
                          </td>
                          
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
