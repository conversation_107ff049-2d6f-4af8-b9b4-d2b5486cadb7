import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../lib/auth';
import { 
  FaUsers, 
  FaUserNurse, 
  FaClipboardList, 
  FaChartLine, 
  FaDollarSign,
  FaStar,
  FaCalendarAlt,
  FaMapMarkerAlt,
  FaPhone,
  FaEnvelope,
  FaEye,
  FaEdit,
  FaCheck,
  FaTimes,
  FaRefresh,
  FaBell,
  FaArrowUp,
  FaArrowDown,
  FaExclamationTriangle,
  FaCheckCircle,
  FaClock,
  FaHeart,
  FaStethoscope,
  FaAward,
  FaShieldAlt,
  FaGlobe,
  FaMobile,
  FaDesktop
} from 'react-icons/fa';
import '../../styles/nurse-dashboard.css';

interface DashboardMetrics {
  totalUsers: number;
  totalNurses: number;
  totalPatients: number;
  totalRequests: number;
  totalApplications: number;
  activeRequests: number;
  completedRequests: number;
  totalRevenue: number;
  averageRating: number;
  responseTime: number;
  completionRate: number;
  userGrowth: number;
  revenueGrowth: number;
}

interface RecentActivity {
  id: string;
  type: 'application' | 'request' | 'completion' | 'registration';
  title: string;
  description: string;
  timestamp: string;
  user: {
    name: string;
    role: string;
    avatar?: string;
  };
  status?: string;
}

interface TopPerformer {
  id: string;
  name: string;
  role: string;
  rating: number;
  completedJobs: number;
  totalEarnings: number;
  avatar?: string;
  specialties: string[];
}

export default function AdminDashboard() {
  const { user, loading } = useAuth();
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [topPerformers, setTopPerformers] = useState<TopPerformer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');

  // Load dashboard data
  const loadDashboardData = useCallback(async () => {
    setIsLoading(true);
    try {
      // Mock data for demonstration
      const mockMetrics: DashboardMetrics = {
        totalUsers: 1247,
        totalNurses: 342,
        totalPatients: 905,
        totalRequests: 2156,
        totalApplications: 4823,
        activeRequests: 89,
        completedRequests: 1967,
        totalRevenue: 125400,
        averageRating: 4.7,
        responseTime: 2.3,
        completionRate: 94.2,
        userGrowth: 12.5,
        revenueGrowth: 18.3
      };

      const mockActivities: RecentActivity[] = [
        {
          id: '1',
          type: 'application',
          title: 'New Application Submitted',
          description: 'Sarah Johnson applied for elderly care request',
          timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
          user: { name: 'Sarah Johnson', role: 'nurse' },
          status: 'pending'
        },
        {
          id: '2',
          type: 'completion',
          title: 'Request Completed',
          description: 'Post-surgery care completed successfully',
          timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
          user: { name: 'Ahmed Hassan', role: 'nurse' },
          status: 'completed'
        },
        {
          id: '3',
          type: 'registration',
          title: 'New Nurse Registration',
          description: 'Fatima Al-Zahra registered as a nurse',
          timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(),
          user: { name: 'Fatima Al-Zahra', role: 'nurse' },
          status: 'pending_approval'
        },
        {
          id: '4',
          type: 'request',
          title: 'New Patient Request',
          description: 'Pediatric care needed in Heliopolis',
          timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(),
          user: { name: 'Mohamed Ali', role: 'patient' },
          status: 'open'
        }
      ];

      const mockTopPerformers: TopPerformer[] = [
        {
          id: '1',
          name: 'Dr. Sarah Johnson',
          role: 'Senior Nurse',
          rating: 4.9,
          completedJobs: 156,
          totalEarnings: 23400,
          specialties: ['Elderly Care', 'Post-Surgery', 'Chronic Care']
        },
        {
          id: '2',
          name: 'Ahmed Hassan',
          role: 'Registered Nurse',
          rating: 4.8,
          completedJobs: 134,
          totalEarnings: 20100,
          specialties: ['Pediatric Care', 'Mental Health']
        },
        {
          id: '3',
          name: 'Fatima Al-Zahra',
          role: 'Certified Nurse',
          rating: 4.7,
          completedJobs: 98,
          totalEarnings: 14700,
          specialties: ['Rehabilitation', 'Elderly Care']
        }
      ];

      setMetrics(mockMetrics);
      setRecentActivities(mockActivities);
      setTopPerformers(mockTopPerformers);
    } catch (err: any) {
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (user?.role === 'admin') {
      loadDashboardData();
    }
  }, [user, loadDashboardData]);

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      loadDashboardData();
    }, 30000);

    return () => clearInterval(interval);
  }, [loadDashboardData]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffMinutes = Math.ceil(diffTime / (1000 * 60));

    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffMinutes < 1440) return `${Math.ceil(diffMinutes / 60)}h ago`;
    return `${Math.ceil(diffMinutes / 1440)}d ago`;
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'application':
        return <FaClipboardList className="h-4 w-4" />;
      case 'request':
        return <FaStethoscope className="h-4 w-4" />;
      case 'completion':
        return <FaCheckCircle className="h-4 w-4" />;
      case 'registration':
        return <FaUserNurse className="h-4 w-4" />;
      default:
        return <FaBell className="h-4 w-4" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'application':
        return 'bg-blue-100 text-blue-600';
      case 'request':
        return 'bg-purple-100 text-purple-600';
      case 'completion':
        return 'bg-green-100 text-green-600';
      case 'registration':
        return 'bg-amber-100 text-amber-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-lg mb-4">
              <div className="animate-spin h-8 w-8 border-4 border-blue-600 border-t-transparent rounded-full"></div>
            </div>
            <h3 className="text-lg font-medium text-slate-900 mb-2">Loading Dashboard</h3>
            <p className="text-sm text-slate-600">Fetching the latest platform metrics...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
            <FaShieldAlt className="h-8 w-8 text-red-600" />
          </div>
          <h3 className="text-lg font-medium text-slate-900 mb-2">Access Denied</h3>
          <p className="text-sm text-slate-600">You don't have permission to access the admin dashboard.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <div className="bg-white shadow-lg border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-lg mr-4">
                <FaStethoscope className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-slate-900 tracking-tight">Admin Dashboard</h1>
                <p className="text-sm text-slate-600 font-medium">Welcome back, {user?.name || 'Admin'}</p>
              </div>
            </div>
            
            {/* Header Actions */}
            <div className="flex items-center space-x-3">
              <select
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange(e.target.value)}
                className="px-3 py-2 border border-slate-300 rounded-lg text-sm bg-white focus:ring-2 focus:ring-blue-500"
              >
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 3 Months</option>
              </select>
              
              <button
                onClick={loadDashboardData}
                disabled={isLoading}
                className="inline-flex items-center px-4 py-2 border border-slate-300 shadow-sm text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 disabled:opacity-50 transition-all duration-200"
              >
                <FaRefresh className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-xl p-4 shadow-sm">
            <div className="flex items-center">
              <FaExclamationTriangle className="h-5 w-5 text-red-600 mr-3" />
              <p className="text-red-700 font-medium">{error}</p>
            </div>
          </div>
        )}

        {/* Key Metrics Cards */}
        {metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Total Users */}
            <div className="bg-white overflow-hidden shadow-lg rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600 mb-1">Total Users</p>
                    <p className="text-2xl font-bold text-slate-900">{metrics.totalUsers.toLocaleString()}</p>
                    <div className="flex items-center mt-2 text-xs">
                      <FaArrowUp className="h-3 w-3 text-emerald-600 mr-1" />
                      <span className="text-emerald-600 font-medium">+{metrics.userGrowth}%</span>
                      <span className="text-slate-500 ml-1">vs last month</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl">
                    <FaUsers className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Active Nurses */}
            <div className="bg-white overflow-hidden shadow-lg rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600 mb-1">Active Nurses</p>
                    <p className="text-2xl font-bold text-slate-900">{metrics.totalNurses}</p>
                    <div className="flex items-center mt-2 text-xs text-slate-600">
                      <FaStethoscope className="h-3 w-3 mr-1" />
                      <span>{Math.round((metrics.totalNurses / metrics.totalUsers) * 100)}% of users</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-emerald-100 rounded-xl">
                    <FaUserNurse className="h-6 w-6 text-emerald-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Total Revenue */}
            <div className="bg-white overflow-hidden shadow-lg rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600 mb-1">Total Revenue</p>
                    <p className="text-2xl font-bold text-slate-900">{formatCurrency(metrics.totalRevenue)}</p>
                    <div className="flex items-center mt-2 text-xs">
                      <FaArrowUp className="h-3 w-3 text-emerald-600 mr-1" />
                      <span className="text-emerald-600 font-medium">+{metrics.revenueGrowth}%</span>
                      <span className="text-slate-500 ml-1">vs last month</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-amber-100 rounded-xl">
                    <FaDollarSign className="h-6 w-6 text-amber-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Platform Rating */}
            <div className="bg-white overflow-hidden shadow-lg rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600 mb-1">Platform Rating</p>
                    <div className="flex items-center space-x-2">
                      <p className="text-2xl font-bold text-slate-900">{metrics.averageRating}</p>
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <FaStar
                            key={i}
                            className={`h-4 w-4 ${
                              i < Math.floor(metrics.averageRating) ? 'text-amber-400' : 'text-slate-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    <p className="text-xs text-slate-600 mt-2">Based on {metrics.completedRequests} reviews</p>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl">
                    <FaAward className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Secondary Metrics */}
        {metrics && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {/* Active Requests */}
            <div className="bg-white rounded-xl border border-slate-200 p-6 shadow-lg">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-slate-900">Active Requests</h3>
                <FaClipboardList className="h-5 w-5 text-blue-600" />
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Active</span>
                  <span className="text-sm font-medium text-slate-900">{metrics.activeRequests}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Completed</span>
                  <span className="text-sm font-medium text-slate-900">{metrics.completedRequests}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Total</span>
                  <span className="text-sm font-medium text-slate-900">{metrics.totalRequests}</span>
                </div>
                <div className="pt-2 border-t border-slate-200">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-slate-700">Completion Rate</span>
                    <span className="text-sm font-bold text-emerald-600">{metrics.completionRate}%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Response Time */}
            <div className="bg-white rounded-xl border border-slate-200 p-6 shadow-lg">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-slate-900">Performance</h3>
                <FaClock className="h-5 w-5 text-amber-600" />
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Avg Response Time</span>
                  <span className="text-sm font-medium text-slate-900">{metrics.responseTime}h</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Applications</span>
                  <span className="text-sm font-medium text-slate-900">{metrics.totalApplications}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Success Rate</span>
                  <span className="text-sm font-medium text-emerald-600">96.2%</span>
                </div>
              </div>
            </div>

            {/* User Distribution */}
            <div className="bg-white rounded-xl border border-slate-200 p-6 shadow-lg">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-slate-900">User Distribution</h3>
                <FaUsers className="h-5 w-5 text-purple-600" />
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Nurses</span>
                  <span className="text-sm font-medium text-slate-900">{metrics.totalNurses}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Patients</span>
                  <span className="text-sm font-medium text-slate-900">{metrics.totalPatients}</span>
                </div>
                <div className="pt-2 border-t border-slate-200">
                  <div className="w-full bg-slate-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full"
                      style={{ width: `${(metrics.totalNurses / metrics.totalUsers) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-slate-500 mt-1">
                    {Math.round((metrics.totalNurses / metrics.totalUsers) * 100)}% nurses, {Math.round((metrics.totalPatients / metrics.totalUsers) * 100)}% patients
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Recent Activities and Top Performers */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Recent Activities */}
          <div className="bg-white rounded-xl border border-slate-200 shadow-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-slate-200 bg-gradient-to-r from-slate-50 to-blue-50">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-slate-900">Recent Activities</h3>
                <FaBell className="h-5 w-5 text-blue-600" />
              </div>
            </div>
            <div className="p-6">
              {recentActivities.length === 0 ? (
                <div className="text-center py-8">
                  <FaBell className="mx-auto h-12 w-12 text-slate-400 mb-4" />
                  <p className="text-slate-500">No recent activities</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-slate-50 transition-colors">
                      <div className={`flex items-center justify-center w-8 h-8 rounded-full ${getActivityColor(activity.type)}`}>
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-slate-900">{activity.title}</p>
                        <p className="text-sm text-slate-600">{activity.description}</p>
                        <div className="flex items-center mt-1 space-x-2">
                          <span className="text-xs text-slate-500">{activity.user.name}</span>
                          <span className="text-xs text-slate-400">•</span>
                          <span className="text-xs text-slate-500">{formatDate(activity.timestamp)}</span>
                        </div>
                      </div>
                      {activity.status && (
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          activity.status === 'completed' ? 'bg-green-100 text-green-800' :
                          activity.status === 'pending' ? 'bg-amber-100 text-amber-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {activity.status.replace('_', ' ')}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Top Performers */}
          <div className="bg-white rounded-xl border border-slate-200 shadow-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-slate-200 bg-gradient-to-r from-slate-50 to-emerald-50">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-slate-900">Top Performers</h3>
                <FaAward className="h-5 w-5 text-emerald-600" />
              </div>
            </div>
            <div className="p-6">
              {topPerformers.length === 0 ? (
                <div className="text-center py-8">
                  <FaAward className="mx-auto h-12 w-12 text-slate-400 mb-4" />
                  <p className="text-slate-500">No performance data available</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {topPerformers.map((performer, index) => (
                    <div key={performer.id} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-slate-50 transition-colors">
                      <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full text-white font-bold text-sm">
                        #{index + 1}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-semibold text-slate-900">{performer.name}</h4>
                          <div className="flex items-center space-x-1">
                            <FaStar className="h-3 w-3 text-amber-400" />
                            <span className="text-sm font-medium text-slate-700">{performer.rating}</span>
                          </div>
                        </div>
                        <p className="text-xs text-slate-600 mb-1">{performer.role}</p>
                        <div className="flex items-center space-x-4 text-xs text-slate-500">
                          <span>{performer.completedJobs} jobs</span>
                          <span>•</span>
                          <span>{formatCurrency(performer.totalEarnings)}</span>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {performer.specialties.slice(0, 2).map((specialty) => (
                            <span key={specialty} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              {specialty}
                            </span>
                          ))}
                          {performer.specialties.length > 2 && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-slate-100 text-slate-600">
                              +{performer.specialties.length - 2} more
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl border border-slate-200 shadow-lg p-6">
          <h3 className="text-lg font-semibold text-slate-900 mb-6">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a
              href="/admin/users"
              className="flex items-center space-x-3 p-4 border border-slate-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 group"
            >
              <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                <FaUsers className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-900">Manage Users</p>
                <p className="text-xs text-slate-600">View and edit user accounts</p>
              </div>
            </a>

            <a
              href="/admin/applications"
              className="flex items-center space-x-3 p-4 border border-slate-200 rounded-lg hover:bg-emerald-50 hover:border-emerald-300 transition-all duration-200 group"
            >
              <div className="flex items-center justify-center w-10 h-10 bg-emerald-100 rounded-lg group-hover:bg-emerald-200 transition-colors">
                <FaClipboardList className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-900">Review Applications</p>
                <p className="text-xs text-slate-600">Process nurse applications</p>
              </div>
            </a>

            <a
              href="/admin/requests"
              className="flex items-center space-x-3 p-4 border border-slate-200 rounded-lg hover:bg-purple-50 hover:border-purple-300 transition-all duration-200 group"
            >
              <div className="flex items-center justify-center w-10 h-10 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                <FaStethoscope className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-900">Monitor Requests</p>
                <p className="text-xs text-slate-600">Track patient requests</p>
              </div>
            </a>

            <a
              href="/admin/statistics"
              className="flex items-center space-x-3 p-4 border border-slate-200 rounded-lg hover:bg-amber-50 hover:border-amber-300 transition-all duration-200 group"
            >
              <div className="flex items-center justify-center w-10 h-10 bg-amber-100 rounded-lg group-hover:bg-amber-200 transition-colors">
                <FaChartLine className="h-5 w-5 text-amber-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-900">View Analytics</p>
                <p className="text-xs text-slate-600">Detailed platform insights</p>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
