.chatWidgetContainer {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.chatButton {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #0070f3;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.chatButton:hover {
  background-color: #0060df;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.chatButton:active {
  transform: translateY(0);
}

.chatButtonIcon {
  font-size: 24px;
  line-height: 1;
}

.chatbox {
  position: absolute;
  bottom: 70px;
  right: 0;
  width: 350px;
  height: 500px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  background: white;
  animation: slideUp 0.3s ease forwards;
}

.chatContainer {
  height: 100%;
  width: 100%;
}

.chatHeader {
  display: flex;
  align-items: center;
  gap: 10px;
}

.errorMessage {
  padding: 10px;
  margin: 10px;
  color: #d32f2f;
  background-color: #fce8e8;
  border-radius: 8px;
  font-size: 14px;
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .chatbox {
    width: calc(100vw - 40px);
    height: 60vh;
    bottom: 80px;
  }
  
  .chatButton {
    width: 50px;
    height: 50px;
  }
}
