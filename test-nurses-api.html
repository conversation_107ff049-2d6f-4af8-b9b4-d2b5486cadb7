<!DOCTYPE html>
<html>
<head>
    <title>Test Nurses API</title>
</head>
<body>
    <h1>Test Nurses API</h1>
    <button onclick="testAPI()">Test API Call</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                console.log('Testing API call...');
                const response = await fetch('http://localhost:3001/api/nurses/nearby?latitude=30.033&longitude=31.233&radius=10', {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);
                
                resultDiv.innerHTML = `
                    <h2>API Response:</h2>
                    <p>Status: ${response.status}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('API Error:', error);
                resultDiv.innerHTML = `
                    <h2>Error:</h2>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
