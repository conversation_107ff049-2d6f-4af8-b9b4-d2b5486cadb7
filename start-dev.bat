@echo off
echo 🏥 Starting Healthcare Platform Development Environment...
echo 📍 Root Directory: %CD%

echo.
echo 🚀 Starting both Frontend and Backend services...
echo 📱 Frontend will be available at: http://localhost:3000
echo 🔧 Backend will be available at: http://localhost:3001
echo 📚 API Documentation at: http://localhost:3001/api/docs
echo.
echo Press Ctrl+C to stop all services
echo.

REM Start both services in parallel using start command
start "Frontend (Port 3000)" cmd /k "cd /d apps\frontend && npm run dev"
start "Backend (Port 3001)" cmd /k "cd /d apps\backend && node dist/main.js"

echo ✅ Services started in separate windows!
echo.
echo To stop services: Close the terminal windows or press Ctrl+C in each
pause
