# Healthcare Platform Development Server Startup Script
# This script starts both frontend and backend services simultaneously

Write-Host "🏥 Starting Healthcare Platform Development Environment..." -ForegroundColor Green
Write-Host "📍 Root Directory: $PWD" -ForegroundColor Cyan

# Function to start frontend
function Start-Frontend {
    Write-Host "🌐 Starting Frontend (Next.js) on port 3000..." -ForegroundColor Cyan
    Set-Location "apps\frontend"
    npm run dev
}

# Function to start backend
function Start-Backend {
    Write-Host "⚙️  Starting Backend (NestJS) on port 3001..." -ForegroundColor Yellow
    Set-Location "apps\backend"
    node dist/main.js
}

# Create jobs to run both services in parallel
Write-Host "🚀 Launching both services in parallel..." -ForegroundColor Green

# Start frontend in background job
$frontendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    Set-Location "apps\frontend"
    npm run dev
}

# Start backend in background job  
$backendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    Set-Location "apps\backend"
    node dist/main.js
}

Write-Host "✅ Services started!" -ForegroundColor Green
Write-Host "📱 Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "🔧 Backend: http://localhost:3001" -ForegroundColor Yellow
Write-Host "📚 API Docs: http://localhost:3001/api/docs" -ForegroundColor Magenta
Write-Host ""
Write-Host "Press Ctrl+C to stop all services" -ForegroundColor Red

# Monitor jobs and display output
try {
    while ($true) {
        # Check if jobs are still running
        if ($frontendJob.State -eq "Failed" -or $backendJob.State -eq "Failed") {
            Write-Host "❌ One or more services failed to start" -ForegroundColor Red
            break
        }
        
        # Display job output
        Receive-Job $frontendJob -Keep | ForEach-Object { Write-Host "[FRONTEND] $_" -ForegroundColor Cyan }
        Receive-Job $backendJob -Keep | ForEach-Object { Write-Host "[BACKEND] $_" -ForegroundColor Yellow }
        
        Start-Sleep -Seconds 1
    }
}
finally {
    # Cleanup jobs when script is terminated
    Write-Host "🛑 Stopping services..." -ForegroundColor Red
    Stop-Job $frontendJob, $backendJob -ErrorAction SilentlyContinue
    Remove-Job $frontendJob, $backendJob -ErrorAction SilentlyContinue
    Write-Host "✅ All services stopped" -ForegroundColor Green
}
